/**
 * MDAC数据收集问题修复脚本
 * 解决"暂无数据可填充"问题
 */

// 主修复函数
function fixDataCollectionIssue() {
    console.log('🔧 开始修复MDAC数据收集问题...');
    
    // 1. 检测问题
    const diagnosis = diagnoseProblem();
    console.log('📊 问题诊断结果:', diagnosis);
    
    // 2. 根据诊断结果应用修复方案
    if (!diagnosis.sidepanelExists) {
        console.log('🚀 侧边栏不存在，尝试修复...');
        return fixSidepanelIssue();
    } else if (!diagnosis.mdacUIExists) {
        console.log('🔧 mdacUI对象不存在，尝试重新初始化...');
        return reinitializeMdacUI();
    } else if (!diagnosis.hasInputData) {
        console.log('📝 没有输入数据，创建临时输入界面...');
        return createTemporaryDataInput();
    } else {
        console.log('✅ 系统状态正常，问题可能在其他地方');
        return testDataCollection();
    }
}

// 问题诊断函数
function diagnoseProblem() {
    const diagnosis = {
        sidepanelExists: false,
        mdacUIExists: false,
        inputElementsExist: false,
        hasInputData: false,
        chromeAPIAvailable: false,
        functionsExist: false
    };
    
    // 检查侧边栏
    const sidepanel = document.querySelector('[id*="sidepanel"], [class*="sidepanel"], [id*="extension"], [class*="extension"]');
    diagnosis.sidepanelExists = !!sidepanel;
    
    // 检查mdacUI对象
    diagnosis.mdacUIExists = !!(window.mdacUI);
    
    // 检查输入元素
    const personalInput = document.getElementById('personalInfoInput');
    const travelInput = document.getElementById('travelInfoInput');
    const accommodationInput = document.getElementById('accommodationInfoInput');
    diagnosis.inputElementsExist = !!(personalInput || travelInput || accommodationInput);
    
    // 检查是否有输入数据
    if (personalInput || travelInput || accommodationInput) {
        diagnosis.hasInputData = !!(
            (personalInput && personalInput.value.trim()) ||
            (travelInput && travelInput.value.trim()) ||
            (accommodationInput && accommodationInput.value.trim())
        );
    }
    
    // 检查Chrome API
    diagnosis.chromeAPIAvailable = !!(typeof chrome !== 'undefined' && chrome.runtime);
    
    // 检查关键函数
    diagnosis.functionsExist = !!(
        (window.mdacUI && typeof window.mdacUI.collectAllFormData === 'function') ||
        (typeof collectAllFormData === 'function')
    );
    
    return diagnosis;
}

// 修复侧边栏问题
function fixSidepanelIssue() {
    console.log('🔧 修复侧边栏问题...');
    
    // 方案1: 尝试通过Chrome API打开侧边栏
    if (typeof chrome !== 'undefined' && chrome.runtime) {
        try {
            chrome.runtime.sendMessage({
                action: 'openSidePanel'
            }, (response) => {
                console.log('侧边栏打开请求已发送:', response);
            });
            
            // 等待侧边栏加载
            setTimeout(() => {
                const diagnosis = diagnoseProblem();
                if (diagnosis.sidepanelExists) {
                    console.log('✅ 侧边栏已成功加载');
                    return true;
                } else {
                    console.log('⚠️ 侧边栏仍未加载，使用备用方案');
                    return createTemporaryDataInput();
                }
            }, 2000);
            
        } catch (error) {
            console.log('❌ Chrome API调用失败:', error.message);
            return showSidepanelPrompt();
        }
    } else {
        console.log('❌ Chrome API不可用');
        return showSidepanelPrompt();
    }
}

// 显示侧边栏提示
function showSidepanelPrompt() {
    console.log('💡 显示侧边栏打开提示...');
    
    // 移除已存在的提示
    const existingPrompt = document.getElementById('sidepanel-prompt');
    if (existingPrompt) existingPrompt.remove();
    
    const prompt = document.createElement('div');
    prompt.id = 'sidepanel-prompt';
    prompt.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #fff3cd;
        border: 2px solid #ffeaa7;
        padding: 25px;
        border-radius: 12px;
        z-index: 10001;
        max-width: 450px;
        text-align: center;
        box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        font-family: Arial, sans-serif;
    `;
    
    prompt.innerHTML = `
        <div style="font-size: 24px; margin-bottom: 15px;">🔧</div>
        <h3 style="margin: 0 0 15px 0; color: #856404;">需要打开MDAC侧边栏</h3>
        <p style="margin: 0 0 20px 0; color: #856404; line-height: 1.5;">
            请按以下步骤操作：<br>
            1. 点击浏览器右上角的MDAC扩展图标<br>
            2. 选择"打开侧边栏"或类似选项<br>
            3. 确保侧边栏在当前页面显示
        </p>
        <div style="display: flex; gap: 10px; justify-content: center;">
            <button onclick="this.parentElement.parentElement.remove(); createTemporaryDataInput();" 
                    style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 6px; cursor: pointer;">
                使用临时输入
            </button>
            <button onclick="this.parentElement.parentElement.remove();" 
                    style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 6px; cursor: pointer;">
                知道了
            </button>
        </div>
    `;
    
    document.body.appendChild(prompt);
    return false;
}

// 重新初始化mdacUI
function reinitializeMdacUI() {
    console.log('🔄 重新初始化mdacUI...');
    
    // 尝试重新加载侧边栏脚本
    try {
        // 这里可以添加重新初始化的逻辑
        console.log('⚠️ mdacUI重新初始化需要侧边栏脚本支持');
        return createTemporaryDataInput();
    } catch (error) {
        console.log('❌ 重新初始化失败:', error.message);
        return createTemporaryDataInput();
    }
}

// 创建临时数据输入界面
function createTemporaryDataInput() {
    console.log('📝 创建临时数据输入界面...');
    
    // 移除已存在的临时输入
    const existingTemp = document.getElementById('temp-data-input');
    if (existingTemp) existingTemp.remove();
    
    const tempDiv = document.createElement('div');
    tempDiv.id = 'temp-data-input';
    tempDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        width: 350px;
        background: white;
        border: 2px solid #007bff;
        border-radius: 12px;
        padding: 20px;
        z-index: 10000;
        box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        font-family: Arial, sans-serif;
        max-height: 80vh;
        overflow-y: auto;
    `;
    
    tempDiv.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
            <h3 style="margin: 0; color: #007bff;">🎯 MDAC数据输入</h3>
            <button onclick="closeTempInput()" style="background: none; border: none; font-size: 20px; cursor: pointer; color: #6c757d;">×</button>
        </div>
        
        <div style="margin-bottom: 15px;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">个人信息:</label>
            <textarea id="tempPersonalInfo" placeholder="例如：&#10;姓名: 张三&#10;护照号: A12345678&#10;出生日期: 15/05/1990&#10;国籍: CHN&#10;性别: M&#10;邮箱: <EMAIL>" 
                      rows="4" style="width: 100%; padding: 8px; border: 1px solid #ced4da; border-radius: 4px; font-size: 12px; resize: vertical;"></textarea>
        </div>
        
        <div style="margin-bottom: 15px;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">旅行信息:</label>
            <textarea id="tempTravelInfo" placeholder="例如：&#10;航班号: MH123&#10;交通方式: AIR&#10;出发地: KUL&#10;到达日期: 15/01/2025&#10;离开日期: 20/01/2025" 
                      rows="3" style="width: 100%; padding: 8px; border: 1px solid #ced4da; border-radius: 4px; font-size: 12px; resize: vertical;"></textarea>
        </div>
        
        <div style="margin-bottom: 20px;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">住宿信息:</label>
            <textarea id="tempAccommodationInfo" placeholder="例如：&#10;住宿类型: 01&#10;地址: LEGOLAND Malaysia Resort Hotel&#10;州属: 01&#10;城市: 0118&#10;邮编: 79250" 
                      rows="3" style="width: 100%; padding: 8px; border: 1px solid #ced4da; border-radius: 4px; font-size: 12px; resize: vertical;"></textarea>
        </div>
        
        <div style="display: flex; flex-direction: column; gap: 8px;">
            <button onclick="processTempData()" 
                    style="width: 100%; padding: 12px; background: #28a745; color: white; border: none; border-radius: 6px; cursor: pointer; font-weight: bold;">
                🚀 解析并填充数据
            </button>
            <button onclick="fillSampleData()" 
                    style="width: 100%; padding: 8px; background: #17a2b8; color: white; border: none; border-radius: 6px; cursor: pointer;">
                📝 填入示例数据
            </button>
            <button onclick="closeTempInput()" 
                    style="width: 100%; padding: 8px; background: #6c757d; color: white; border: none; border-radius: 6px; cursor: pointer;">
                关闭
            </button>
        </div>
        
        <div id="tempStatus" style="margin-top: 15px; padding: 10px; border-radius: 4px; display: none;"></div>
    `;
    
    document.body.appendChild(tempDiv);
    
    // 添加全局函数
    window.processTempData = processTempData;
    window.fillSampleData = fillSampleData;
    window.closeTempInput = closeTempInput;
    
    console.log('✅ 临时数据输入界面已创建');
    return true;
}

// 处理临时数据
function processTempData() {
    console.log('🔄 处理临时数据...');
    
    const personalInfo = document.getElementById('tempPersonalInfo').value.trim();
    const travelInfo = document.getElementById('tempTravelInfo').value.trim();
    const accommodationInfo = document.getElementById('tempAccommodationInfo').value.trim();
    
    if (!personalInfo && !travelInfo && !accommodationInfo) {
        showTempStatus('请至少填入一种类型的信息', 'warning');
        return;
    }
    
    try {
        // 解析数据
        const parsedData = parseAllData(personalInfo, travelInfo, accommodationInfo);
        console.log('解析结果:', parsedData);
        
        if (Object.keys(parsedData).length === 0) {
            showTempStatus('数据解析失败，请检查输入格式', 'error');
            return;
        }
        
        // 填充到MDAC表单
        const fillResult = fillMDACForm(parsedData);
        
        if (fillResult.success) {
            showTempStatus(`成功填充 ${fillResult.filledCount} 个字段`, 'success');
            
            // 3秒后自动关闭
            setTimeout(() => {
                closeTempInput();
            }, 3000);
        } else {
            showTempStatus(`填充部分成功：${fillResult.filledCount}/${fillResult.totalCount} 个字段`, 'warning');
        }
        
    } catch (error) {
        console.error('处理数据时出错:', error);
        showTempStatus('处理数据时出错: ' + error.message, 'error');
    }
}

// 填入示例数据
function fillSampleData() {
    document.getElementById('tempPersonalInfo').value = `姓名: 张伟
护照号: A12345678
出生日期: 15/05/1990
国籍: CHN
性别: M
邮箱: <EMAIL>
电话: +60123456789`;

    document.getElementById('tempTravelInfo').value = `航班号: MH123
交通方式: AIR
出发地: KUL
到达日期: 15/01/2025
离开日期: 20/01/2025`;

    document.getElementById('tempAccommodationInfo').value = `住宿类型: 01
地址: LEGOLAND Malaysia Resort Hotel
州属: 01
城市: 0118
邮编: 79250`;

    showTempStatus('示例数据已填入，点击"解析并填充数据"继续', 'info');
}

// 显示临时状态
function showTempStatus(message, type) {
    const statusDiv = document.getElementById('tempStatus');
    if (!statusDiv) return;
    
    const colors = {
        success: '#d4edda',
        error: '#f8d7da',
        warning: '#fff3cd',
        info: '#d1ecf1'
    };
    
    statusDiv.style.display = 'block';
    statusDiv.style.backgroundColor = colors[type] || colors.info;
    statusDiv.style.color = '#495057';
    statusDiv.textContent = message;
    
    // 3秒后自动隐藏（除非是成功消息）
    if (type !== 'success') {
        setTimeout(() => {
            statusDiv.style.display = 'none';
        }, 3000);
    }
}

// 关闭临时输入
function closeTempInput() {
    const tempDiv = document.getElementById('temp-data-input');
    if (tempDiv) {
        tempDiv.remove();
        console.log('✅ 临时输入界面已关闭');
    }
}

// 解析所有数据
function parseAllData(personalInfo, travelInfo, accommodationInfo) {
    const allData = {};
    
    // 解析个人信息
    if (personalInfo) {
        Object.assign(allData, parseTextData(personalInfo, 'personal'));
    }
    
    // 解析旅行信息
    if (travelInfo) {
        Object.assign(allData, parseTextData(travelInfo, 'travel'));
    }
    
    // 解析住宿信息
    if (accommodationInfo) {
        Object.assign(allData, parseTextData(accommodationInfo, 'accommodation'));
    }
    
    return allData;
}

// 简化的文本解析函数
function parseTextData(text, type) {
    const data = {};
    const lines = text.split('\n');
    
    lines.forEach(line => {
        const match = line.match(/^([^:：]+)[:：]\s*(.+)$/);
        if (match) {
            const key = match[1].trim();
            const value = match[2].trim();
            
            // 字段映射
            const fieldMap = {
                '姓名': 'name',
                'name': 'name',
                '护照号': 'passportNo',
                'passport': 'passportNo',
                '出生日期': 'dateOfBirth',
                'birth': 'dateOfBirth',
                '国籍': 'nationality',
                'nationality': 'nationality',
                '性别': 'sex',
                'sex': 'sex',
                '邮箱': 'email',
                'email': 'email',
                '电话': 'mobileNo',
                'phone': 'mobileNo',
                '航班号': 'flightNo',
                'flight': 'flightNo',
                '交通方式': 'modeOfTravel',
                'travel': 'modeOfTravel',
                '出发地': 'lastPort',
                'from': 'lastPort',
                '到达日期': 'arrivalDate',
                'arrival': 'arrivalDate',
                '离开日期': 'departureDate',
                'departure': 'departureDate',
                '住宿类型': 'accommodation',
                'accommodation': 'accommodation',
                '地址': 'address',
                'address': 'address',
                '州属': 'state',
                'state': 'state',
                '城市': 'city',
                'city': 'city',
                '邮编': 'postcode',
                'postcode': 'postcode'
            };
            
            const mappedKey = fieldMap[key.toLowerCase()] || fieldMap[key];
            if (mappedKey) {
                data[mappedKey] = value;
            }
        }
    });
    
    return data;
}

// 填充MDAC表单
function fillMDACForm(data) {
    console.log('🎯 开始填充MDAC表单:', data);
    
    let filledCount = 0;
    let totalCount = 0;
    
    // 字段映射到MDAC表单ID
    const fieldMapping = {
        name: 'name',
        passportNo: 'passNo',
        dateOfBirth: 'dob',
        nationality: 'nationality',
        sex: 'sex',
        email: 'email',
        mobileNo: 'mobile',
        flightNo: 'vesselNm',
        modeOfTravel: 'trvlMode',
        lastPort: 'embark',
        arrivalDate: 'arrDt',
        departureDate: 'depDt',
        accommodation: 'accommodationStay',
        address: 'accommodationAddress1',
        state: 'accommodationState',
        city: 'accommodationCity',
        postcode: 'accommodationPostcode'
    };
    
    Object.entries(data).forEach(([key, value]) => {
        const mdacFieldId = fieldMapping[key];
        if (mdacFieldId) {
            totalCount++;
            const element = document.getElementById(mdacFieldId);
            if (element) {
                element.value = value;
                element.dispatchEvent(new Event('input', { bubbles: true }));
                element.dispatchEvent(new Event('change', { bubbles: true }));
                filledCount++;
                console.log(`✅ 填充字段 ${mdacFieldId}: "${value}"`);
            } else {
                console.log(`❌ 字段 ${mdacFieldId} 不存在`);
            }
        }
    });
    
    return {
        success: filledCount > 0,
        filledCount,
        totalCount
    };
}

// 测试数据收集
function testDataCollection() {
    console.log('🧪 测试数据收集功能...');
    
    try {
        let collectFunction = null;
        
        if (window.mdacUI && typeof window.mdacUI.collectAllFormData === 'function') {
            collectFunction = window.mdacUI.collectAllFormData.bind(window.mdacUI);
        } else if (typeof collectAllFormData === 'function') {
            collectFunction = collectAllFormData;
        }
        
        if (collectFunction) {
            const data = collectFunction();
            console.log('数据收集测试结果:', data);
            
            if (Object.keys(data).length > 0) {
                console.log('✅ 数据收集功能正常');
                return true;
            } else {
                console.log('⚠️ 数据收集返回空结果');
                return createTemporaryDataInput();
            }
        } else {
            console.log('❌ 数据收集函数不存在');
            return createTemporaryDataInput();
        }
    } catch (error) {
        console.log('❌ 数据收集测试失败:', error.message);
        return createTemporaryDataInput();
    }
}

// 自动运行修复
console.log('🚀 MDAC数据收集问题修复脚本已加载');
console.log('💡 运行 fixDataCollectionIssue() 开始修复');

// 如果在浏览器环境中，延迟自动运行
if (typeof window !== 'undefined') {
    setTimeout(() => {
        console.log('🔧 自动运行修复检查...');
        fixDataCollectionIssue();
    }, 2000);
}

// 导出函数
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        fixDataCollectionIssue,
        diagnoseProblem,
        createTemporaryDataInput
    };
}
