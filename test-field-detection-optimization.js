/**
 * MDAC字段检测优化测试脚本
 * 验证字段检测成功率从81%提升到100%
 */

// 测试字段检测优化
async function testFieldDetectionOptimization() {
    console.log('🧪 开始测试MDAC字段检测优化...');
    
    // 1. 检查FormFieldDetector是否已加载
    console.log('\n📋 检查FormFieldDetector:');
    if (typeof FormFieldDetector !== 'undefined') {
        console.log('✅ FormFieldDetector类已加载');
    } else {
        console.log('❌ FormFieldDetector类未加载');
        return;
    }
    
    // 2. 创建检测器实例
    const detector = new FormFieldDetector();
    console.log('✅ 字段检测器实例已创建');
    
    // 3. 定义预期的21个字段
    const expectedFields = [
        'name', 'passportNo', 'dateOfBirth', 'nationality', 'sex', 
        'passportExpiry', 'email', 'confirmEmail', 'countryCode', 'mobileNo',
        'arrivalDate', 'departureDate', 'flightNo', 'modeOfTravel', 'lastPort',
        'accommodation', 'address', 'addressLine2', 'state', 'city', 'postcode'
    ];
    
    console.log(`\n🎯 预期检测字段数量: ${expectedFields.length}`);
    console.log('预期字段列表:', expectedFields.join(', '));
    
    // 4. 执行字段检测
    console.log('\n🔍 开始执行字段检测...');
    
    try {
        const detectedFields = await detector.detectFormFields();
        
        console.log('\n📊 检测结果统计:');
        console.log(`检测到的字段数量: ${Object.keys(detectedFields).length}`);
        console.log(`预期字段数量: ${expectedFields.length}`);
        
        const detectionRate = (Object.keys(detectedFields).length / expectedFields.length * 100).toFixed(1);
        console.log(`检测成功率: ${detectionRate}%`);
        
        // 5. 详细分析检测结果
        console.log('\n✅ 成功检测的字段:');
        const detectedFieldNames = Object.keys(detectedFields);
        detectedFieldNames.forEach((fieldName, index) => {
            const element = detectedFields[fieldName];
            console.log(`${index + 1}. ${fieldName}: ID="${element.id}" | Name="${element.name}" | Type="${element.type}"`);
        });
        
        // 6. 分析未检测的字段
        console.log('\n❌ 未检测的字段:');
        const missedFields = expectedFields.filter(field => !detectedFields[field]);
        if (missedFields.length === 0) {
            console.log('🎉 所有字段都已成功检测！');
        } else {
            missedFields.forEach((fieldName, index) => {
                console.log(`${index + 1}. ${fieldName}`);
                
                // 尝试手动查找这个字段
                const possibleElements = document.querySelectorAll(`#${fieldName}, [name="${fieldName}"]`);
                if (possibleElements.length > 0) {
                    console.log(`   💡 可能的元素: ID="${possibleElements[0].id}" | Name="${possibleElements[0].name}"`);
                }
            });
        }
        
        // 7. 验证关键的4个之前未检测字段
        console.log('\n🎯 验证关键的4个优化字段:');
        const keyOptimizedFields = [
            { field: 'passportExpiry', expectedId: 'passExpDte' },
            { field: 'countryCode', expectedId: 'region' },
            { field: 'arrivalDate', expectedId: 'arrDt' },
            { field: 'departureDate', expectedId: 'depDt' }
        ];
        
        let optimizedFieldsDetected = 0;
        keyOptimizedFields.forEach(({ field, expectedId }) => {
            if (detectedFields[field]) {
                console.log(`✅ ${field}: 检测成功 (ID: ${detectedFields[field].id})`);
                optimizedFieldsDetected++;
            } else {
                console.log(`❌ ${field}: 检测失败`);
                
                // 检查元素是否存在
                const element = document.getElementById(expectedId);
                if (element) {
                    console.log(`   💡 元素存在但未被检测: ID="${element.id}" | Type="${element.type}"`);
                } else {
                    console.log(`   ⚠️ 元素不存在: ${expectedId}`);
                }
            }
        });
        
        console.log(`\n📈 关键优化字段检测率: ${optimizedFieldsDetected}/4 (${(optimizedFieldsDetected/4*100).toFixed(1)}%)`);
        
        // 8. 检测质量分析
        console.log('\n🔍 检测质量分析:');
        const validation = detector.validateDetection(detectedFields);
        console.log('验证结果:', validation);
        
        // 9. 性能统计
        console.log('\n⚡ 性能统计:');
        const stats = detector.getDetectionStats();
        console.log('检测统计:', stats);
        
        // 10. 对比优化前后的效果
        console.log('\n📊 优化效果对比:');
        console.log('优化前检测率: 81% (17/21)');
        console.log(`优化后检测率: ${detectionRate}% (${Object.keys(detectedFields).length}/${expectedFields.length})`);
        
        const improvement = parseFloat(detectionRate) - 81;
        if (improvement > 0) {
            console.log(`🎉 检测率提升: +${improvement.toFixed(1)}%`);
        } else if (improvement === 0) {
            console.log('📊 检测率保持不变');
        } else {
            console.log(`📉 检测率下降: ${improvement.toFixed(1)}%`);
        }
        
        // 11. 生成详细报告
        const report = {
            timestamp: new Date().toISOString(),
            totalExpectedFields: expectedFields.length,
            detectedFieldsCount: Object.keys(detectedFields).length,
            detectionRate: parseFloat(detectionRate),
            detectedFields: detectedFieldNames,
            missedFields: missedFields,
            keyOptimizedFields: {
                detected: optimizedFieldsDetected,
                total: 4,
                rate: (optimizedFieldsDetected/4*100).toFixed(1) + '%'
            },
            validation: validation,
            stats: stats,
            improvement: improvement.toFixed(1) + '%'
        };
        
        // 保存报告到全局变量
        window.fieldDetectionReport = report;
        console.log('\n📋 详细报告已保存到 window.fieldDetectionReport');
        
        // 12. 最终结论
        console.log('\n🎯 最终结论:');
        if (parseFloat(detectionRate) >= 100) {
            console.log('🎉 目标达成！字段检测成功率已达到100%');
        } else if (parseFloat(detectionRate) >= 95) {
            console.log('✅ 优秀！字段检测成功率已达到95%以上');
        } else if (parseFloat(detectionRate) > 81) {
            console.log('📈 良好！字段检测成功率有所提升');
        } else {
            console.log('⚠️ 需要进一步优化字段检测逻辑');
        }
        
        return report;
        
    } catch (error) {
        console.error('❌ 字段检测过程中发生错误:', error);
        
        // 记录错误日志
        if (window.mdacLogger) {
            window.mdacLogger.error('FORM', '字段检测测试失败', {
                error: error.message,
                stack: error.stack
            });
        }
        
        return null;
    }
}

// 测试特定字段的检测逻辑
function testSpecificFieldDetection(fieldName) {
    console.log(`🔍 测试特定字段检测: ${fieldName}`);
    
    if (typeof FormFieldDetector === 'undefined') {
        console.log('❌ FormFieldDetector未加载');
        return;
    }
    
    const detector = new FormFieldDetector();
    const patterns = detector.fieldPatterns[fieldName];
    
    if (!patterns) {
        console.log(`❌ 字段 ${fieldName} 的模式未定义`);
        return;
    }
    
    console.log('字段模式:', patterns);
    
    // 获取所有表单元素
    const allElements = detector.getAllFormElements();
    console.log(`总表单元素数量: ${allElements.length}`);
    
    // 测试每个元素的匹配分数
    console.log('\n元素匹配分数:');
    allElements.forEach((element, index) => {
        if (element.id || element.name) {
            const score = detector.calculateMatchScore(element, patterns);
            if (score > 0) {
                console.log(`${index + 1}. ID="${element.id}" | Name="${element.name}" | 分数: ${score}`);
            }
        }
    });
    
    // 执行检测
    const detected = detector.detectFieldByPatterns(fieldName, patterns, allElements);
    if (detected) {
        console.log(`✅ 检测成功: ID="${detected.id}" | Name="${detected.name}"`);
    } else {
        console.log(`❌ 检测失败`);
    }
}

// 自动运行测试（如果在浏览器环境中）
if (typeof window !== 'undefined' && document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(testFieldDetectionOptimization, 3000);
    });
} else if (typeof window !== 'undefined') {
    setTimeout(testFieldDetectionOptimization, 3000);
}

// 导出测试函数（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { 
        testFieldDetectionOptimization,
        testSpecificFieldDetection
    };
}
