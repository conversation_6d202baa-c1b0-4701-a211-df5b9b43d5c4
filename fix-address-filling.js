/**
 * 住宿地址填充问题修复脚本
 * 修复Travel Information中住宿地址字段不被填充的问题
 */

console.log('🛠️ 加载住宿地址填充修复脚本...');

// 1. 修复parseAIResponse中地址字段提取
function enhanceAddressExtraction() {
    // 如果window.ui已经加载，增强其parseAIResponse方法
    if (window.ui && typeof window.ui.parseAIResponse === 'function') {
        const originalParseAIResponse = window.ui.parseAIResponse.bind(window.ui);
        
        window.ui.parseAIResponse = function(response) {
            console.log('🔧 使用增强版parseAIResponse解析响应:', response);
            
            const result = originalParseAIResponse(response);
            
            // 确保地址字段被正确提取
            if (result && typeof result === 'object') {
                // 如果有accommodationAddress但没有address，复制一份
                if (result.accommodationAddress && !result.address) {
                    result.address = result.accommodationAddress;
                    console.log('✅ 复制accommodationAddress到address字段:', result.address);
                }
                
                // 如果有address但没有accommodationAddress，复制一份
                if (result.address && !result.accommodationAddress) {
                    result.accommodationAddress = result.address;
                    console.log('✅ 复制address到accommodationAddress字段:', result.accommodationAddress);
                }
            }
            
            console.log('🎯 增强后的解析结果:', result);
            return result;
        };
        
        console.log('✅ parseAIResponse方法已增强');
    }
}

// 2. 修复fillTravelFields中的字段处理
function enhanceFillTravelFields() {
    if (window.ui && typeof window.ui.fillTravelFields === 'function') {
        const originalFillTravelFields = window.ui.fillTravelFields.bind(window.ui);
        
        window.ui.fillTravelFields = async function(data) {
            console.log('🔧 使用增强版fillTravelFields填充字段:', data);
            
            // 预处理数据，确保地址字段存在
            if (data && typeof data === 'object') {
                // 确保address字段存在
                if (!data.address && data.accommodationAddress) {
                    data.address = data.accommodationAddress;
                    console.log('✅ 使用accommodationAddress作为address:', data.address);
                }
                
                // 确保accommodationAddress字段存在
                if (!data.accommodationAddress && data.address) {
                    data.accommodationAddress = data.address;
                    console.log('✅ 使用address作为accommodationAddress:', data.accommodationAddress);
                }
                
                // 强制填充地址字段，即使其他字段失败
                const addressField = document.getElementById('address');
                if (addressField && (data.address || data.accommodationAddress)) {
                    const addressValue = data.address || data.accommodationAddress;
                    try {
                        addressField.value = addressValue;
                        addressField.dispatchEvent(new Event('input', { bubbles: true }));
                        addressField.dispatchEvent(new Event('change', { bubbles: true }));
                        addressField.classList.add('filled');
                        console.log('✅ 强制填充地址字段成功:', addressValue);
                    } catch (error) {
                        console.error('❌ 强制填充地址字段失败:', error);
                    }
                }
            }
            
            // 调用原始方法
            return await originalFillTravelFields(data);
        };
        
        console.log('✅ fillTravelFields方法已增强');
    }
}

// 3. 增强Google Maps集成
function enhanceGoogleMapsIntegration() {
    if (window.ui && window.ui.googleMaps) {
        console.log('🗺️ Google Maps集成已存在，添加调试日志');
        
        // 监听Google Maps标准化结果
        const originalStandardizeAddress = window.ui.googleMaps.standardizeAddress;
        if (originalStandardizeAddress) {
            window.ui.googleMaps.standardizeAddress = async function(address) {
                console.log('🌍 Google Maps标准化地址:', address);
                
                const result = await originalStandardizeAddress.call(this, address);
                
                console.log('📍 Google Maps标准化结果:', result);
                
                if (result && result.success && result.mdacMapping) {
                    console.log('🎯 MDAC映射字段:', result.mdacMapping);
                }
                
                return result;
            };
        }
    }
}

// 4. 添加实时字段填充监控
function addFieldFillMonitor() {
    // 监控地址字段的变化
    const addressField = document.getElementById('address');
    if (addressField) {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'value') {
                    console.log('🏠 地址字段值变化:', addressField.value);
                }
            });
        });
        
        observer.observe(addressField, {
            attributes: true,
            attributeFilter: ['value']
        });
        
        // 监听input事件
        addressField.addEventListener('input', (e) => {
            console.log('🏠 地址字段输入事件:', e.target.value);
        });
        
        // 监听change事件
        addressField.addEventListener('change', (e) => {
            console.log('🏠 地址字段变更事件:', e.target.value);
        });
        
        console.log('✅ 地址字段监控已启用');
    }
}

// 5. 添加手动测试函数
function addManualTestFunction() {
    window.testAddressFilling = function(testAddress = "KUALA LUMPUR CITY CENTER HOTEL") {
        console.log('🧪 开始手动测试地址填充:', testAddress);
        
        const mockData = {
            address: testAddress,
            accommodationAddress: testAddress,
            accommodation: "2", // Hotel
            state: "14", // Kuala Lumpur
            city: "1400", // Kuala Lumpur
            postcode: "50000"
        };
        
        console.log('📊 测试数据:', mockData);
        
        if (window.ui && window.ui.fillTravelFields) {
            window.ui.fillTravelFields(mockData);
        } else {
            console.error('❌ fillTravelFields方法不可用');
        }
    };
    
    console.log('✅ 手动测试函数已添加：testAddressFilling()');
}

// 6. 主修复函数
function applyAddressFillingFix() {
    console.log('🚀 开始应用住宿地址填充修复...');
    
    // 等待UI初始化
    const checkUIReady = () => {
        if (window.ui) {
            console.log('✅ UI已初始化，开始应用修复');
            
            enhanceAddressExtraction();
            enhanceFillTravelFields();
            enhanceGoogleMapsIntegration();
            addFieldFillMonitor();
            addManualTestFunction();
            
            console.log('🎉 住宿地址填充修复已完成');
            
            // 显示状态消息（如果UI支持）
            if (window.ui.showMessage) {
                window.ui.showMessage('住宿地址填充修复已应用', 'success');
            }
        } else {
            console.log('⏳ 等待UI初始化...');
            setTimeout(checkUIReady, 500);
        }
    };
    
    checkUIReady();
}

// 7. 自动应用修复
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', applyAddressFillingFix);
} else {
    applyAddressFillingFix();
}

// 导出修复函数
window.addressFillingFix = {
    apply: applyAddressFillingFix,
    testAddressFilling: () => window.testAddressFilling && window.testAddressFilling(),
    enhanceAddressExtraction,
    enhanceFillTravelFields,
    enhanceGoogleMapsIntegration
};

console.log('🎯 住宿地址填充修复脚本已加载');
console.log('💡 使用方法：');
console.log('   - 自动应用: addressFillingFix.apply()');
console.log('   - 手动测试: testAddressFilling("测试地址")');
