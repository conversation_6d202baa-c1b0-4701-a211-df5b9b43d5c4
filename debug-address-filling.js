/**
 * 住宿地址填充问题调试脚本
 * 用于排查为什么住宿地址字段没有被正确填充
 */

console.log('🔍 开始调试住宿地址填充问题...');

// 1. 检查Google Maps API是否正常工作
function testGoogleMapsAPI() {
    console.log('🌍 测试Google Maps API...');
    
    if (typeof GoogleMapsIntegration === 'undefined') {
        console.error('❌ GoogleMapsIntegration 类未找到');
        return false;
    }
    
    const apiKey = window.MDAC_AI_CONFIG?.GEMINI_CONFIG?.DEFAULT_API_KEY || 
                   (typeof GEMINI_CONFIG !== 'undefined' ? GEMINI_CONFIG.DEFAULT_API_KEY : null);
    
    if (!apiKey) {
        console.error('❌ API密钥未找到');
        return false;
    }
    
    try {
        const googleMaps = new GoogleMapsIntegration(apiKey);
        console.log('✅ Google Maps集成实例创建成功');
        return googleMaps;
    } catch (error) {
        console.error('❌ Google Maps集成初始化失败:', error);
        return false;
    }
}

// 2. 测试地址标准化功能
async function testAddressStandardization(googleMaps, testAddress = "KUALA LUMPUR CITY CENTER HOTEL") {
    console.log(`🏨 测试地址标准化: ${testAddress}`);
    
    try {
        const result = await googleMaps.standardizeAddress(testAddress);
        console.log('📍 标准化结果:', result);
        
        if (result.success) {
            console.log('✅ 地址标准化成功');
            console.log('🗺️ MDAC映射:', result.mdacMapping);
            console.log('📋 标准化地址:', result.standardizedAddress);
            return result;
        } else {
            console.error('❌ 地址标准化失败:', result.error);
            return null;
        }
    } catch (error) {
        console.error('❌ 地址标准化异常:', error);
        return null;
    }
}

// 3. 检查HTML字段映射
function checkFieldMapping() {
    console.log('🔍 检查HTML字段映射...');
    
    const addressField = document.getElementById('address');
    const stateField = document.getElementById('state');
    const cityField = document.getElementById('city');
    const postcodeField = document.getElementById('postcode');
    
    console.log('🏠 住宿地址字段:', addressField);
    console.log('🏛️ 州属字段:', stateField);
    console.log('🏙️ 城市字段:', cityField);
    console.log('📮 邮政编码字段:', postcodeField);
    
    return {
        address: addressField,
        state: stateField,
        city: cityField,
        postcode: postcodeField
    };
}

// 4. 模拟完整的数据流
async function simulateDataFlow() {
    console.log('🔄 模拟完整数据流...');
    
    // 步骤1: 初始化Google Maps
    const googleMaps = testGoogleMapsAPI();
    if (!googleMaps) return;
    
    // 步骤2: 检查字段映射
    const fields = checkFieldMapping();
    
    // 步骤3: 模拟AI解析的数据
    const mockTravelData = {
        accommodationAddress: "KUALA LUMPUR CITY CENTER HOTEL",
        accommodation: "2", // Hotel
        // 其他字段...
    };
    
    console.log('📝 模拟AI解析数据:', mockTravelData);
    
    // 步骤4: 测试Google Maps标准化
    if (mockTravelData.accommodationAddress) {
        const standardizedResult = await testAddressStandardization(googleMaps, mockTravelData.accommodationAddress);
        
        if (standardizedResult && standardizedResult.mdacMapping) {
            // 模拟标准化后的数据合并
            Object.assign(mockTravelData, standardizedResult.mdacMapping);
            mockTravelData.accommodationAddress = standardizedResult.standardizedAddress;
            console.log('🔄 合并后的数据:', mockTravelData);
        }
    }
    
    // 步骤5: 测试字段填充逻辑
    testFieldFilling(mockTravelData, fields);
}

// 5. 测试字段填充逻辑
function testFieldFilling(data, fields) {
    console.log('✏️ 测试字段填充逻辑...');
    console.log('📊 输入数据:', data);
    
    // 字段映射（从ui-sidepanel.js复制）
    const fieldMap = {
        'address': 'address',
        'accommodationAddress': 'address',
        'state': 'state',
        'accommodationState': 'state',
        'city': 'city',
        'accommodationCity': 'city',
        'postcode': 'postcode',
        'accommodationPostcode': 'postcode'
    };
    
    console.log('🗺️ 字段映射:', fieldMap);
    
    // 测试每个字段的映射
    Object.keys(data).forEach(dataKey => {
        const value = data[dataKey];
        if (value !== undefined && value !== null && value !== '') {
            const elementId = fieldMap[dataKey] || dataKey;
            const element = fields[elementId];
            
            console.log(`🔍 数据键: ${dataKey}, 值: ${value}, 目标元素ID: ${elementId}, 元素存在: ${!!element}`);
            
            if (element) {
                console.log(`✅ 可以填充字段 ${elementId} = ${value}`);
                // 这里可以实际填充字段进行测试
                // element.value = value;
            } else {
                console.warn(`❌ 字段 ${elementId} 不存在，无法填充`);
            }
        }
    });
}

// 6. 运行调试
async function runDebug() {
    console.log('🚀 开始住宿地址填充调试...');
    console.log('=====================================');
    
    await simulateDataFlow();
    
    console.log('=====================================');
    console.log('✅ 调试完成');
}

// 导出调试函数
window.debugAddressFilling = {
    runDebug,
    testGoogleMapsAPI,
    testAddressStandardization,
    checkFieldMapping,
    simulateDataFlow,
    testFieldFilling
};

console.log('🛠️ 住宿地址填充调试工具已加载');
console.log('💡 使用方法: 在控制台运行 debugAddressFilling.runDebug()');
