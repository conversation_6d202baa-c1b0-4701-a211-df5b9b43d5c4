/**
 * MDAC日志系统测试脚本
 * 用于验证日志记录和调试控制台功能
 */

// 测试日志系统功能
function testLoggingSystem() {
    console.log('🧪 开始测试MDAC日志系统...');
    
    // 1. 检查日志管理器是否已加载
    console.log('\n📋 检查日志管理器:');
    if (typeof window.mdacLogger !== 'undefined') {
        console.log('✅ MDACLogger已加载');
        
        // 测试基本日志功能
        console.log('\n📝 测试基本日志功能:');
        window.mdacLogger.debug('TEST', '这是一条DEBUG日志');
        window.mdacLogger.info('TEST', '这是一条INFO日志');
        window.mdacLogger.warn('TEST', '这是一条WARN日志');
        window.mdacLogger.error('TEST', '这是一条ERROR日志');
        
        // 测试带数据的日志
        window.mdacLogger.info('TEST', '带数据的日志', {
            testData: 'test value',
            number: 123,
            array: [1, 2, 3]
        });
        
        // 测试性能监控
        console.log('\n⏱️ 测试性能监控:');
        window.mdacLogger.startPerformance('testOperation');
        setTimeout(() => {
            window.mdacLogger.endPerformance('testOperation');
        }, 100);
        
        // 测试日志统计
        console.log('\n📊 测试日志统计:');
        const stats = window.mdacLogger.getStats();
        console.log('日志统计:', stats);
        
        // 测试日志过滤
        console.log('\n🔍 测试日志过滤:');
        const errorLogs = window.mdacLogger.getLogs({ level: 'ERROR' });
        console.log('ERROR级别日志数量:', errorLogs.length);
        
        const testModuleLogs = window.mdacLogger.getLogs({ module: 'TEST' });
        console.log('TEST模块日志数量:', testModuleLogs.length);
        
    } else {
        console.log('❌ MDACLogger未加载');
    }
    
    // 2. 检查调试控制台是否已加载
    console.log('\n🐛 检查调试控制台:');
    if (typeof window.mdacDebugConsole !== 'undefined') {
        console.log('✅ MDACDebugConsole已加载');
        
        // 测试控制台显示
        console.log('\n📺 测试控制台显示:');
        window.mdacDebugConsole.show();
        
        setTimeout(() => {
            console.log('控制台可见性:', window.mdacDebugConsole.isVisible);
            
            // 测试统计更新
            window.mdacDebugConsole.updateStats();
            
            // 测试过滤功能
            console.log('\n🔧 测试过滤功能:');
            window.mdacDebugConsole.currentFilter = { level: 'INFO' };
            window.mdacDebugConsole.applyFilter();
            
            // 清除过滤器
            setTimeout(() => {
                window.mdacDebugConsole.clearFilter();
            }, 1000);
            
        }, 500);
        
    } else {
        console.log('❌ MDACDebugConsole未加载');
    }
    
    // 3. 测试模拟AI解析过程
    console.log('\n🤖 模拟AI解析过程日志:');
    if (window.mdacLogger) {
        // 模拟个人信息解析
        window.mdacLogger.info('AI', '开始解析个人信息');
        window.mdacLogger.startPerformance('parsePersonalInfo');
        
        setTimeout(() => {
            window.mdacLogger.debug('AI', '个人信息内容长度: 150字符', {
                preview: '张三，护照号：A12345678，出生日期：1990-05-15...'
            });
            
            window.mdacLogger.info('AI', '开始调用Gemini API解析个人信息');
            
            setTimeout(() => {
                window.mdacLogger.info('AI', 'Gemini API返回结果成功');
                window.mdacLogger.debug('AI', 'AI原始响应', { 
                    response: '{"name":"ZHANG SAN","passportNo":"A12345678",...}' 
                });
                
                window.mdacLogger.info('AI', '个人信息解析完成', {
                    fieldsCount: 6,
                    fields: ['name', 'passportNo', 'dateOfBirth', 'nationality', 'sex', 'email']
                });
                
                const duration = window.mdacLogger.endPerformance('parsePersonalInfo');
                window.mdacLogger.info('AI', `个人信息解析总耗时: ${duration}ms`);
                
            }, 200);
        }, 100);
    }
    
    // 4. 测试模拟表单填充过程
    console.log('\n📝 模拟表单填充过程日志:');
    if (window.mdacLogger) {
        setTimeout(() => {
            window.mdacLogger.info('FORM', '开始增强表单填充');
            window.mdacLogger.startPerformance('enhancedFormFill');
            
            window.mdacLogger.debug('FORM', '表单数据概览', {
                fieldsCount: 8,
                fields: ['name', 'passportNo', 'flightNo', 'address']
            });
            
            // 模拟字段填充
            const testFields = [
                { name: 'name', success: true },
                { name: 'passportNo', success: true },
                { name: 'flightNo', success: true },
                { name: 'arrivalDate', success: false, reason: 'MDAC网站在当前页面不收集此信息' },
                { name: 'address', success: true }
            ];
            
            testFields.forEach((field, index) => {
                setTimeout(() => {
                    if (field.success) {
                        window.mdacLogger.debug('FORM', `开始填充字段: ${field.name}`);
                        window.mdacLogger.debug('FORM', `字段 ${field.name} 元素找到`, {
                            tagName: 'INPUT',
                            id: field.name,
                            type: 'text'
                        });
                        window.mdacLogger.info('FORM', `字段 ${field.name} 填充成功`);
                    } else {
                        window.mdacLogger.warn('FORM', `字段 ${field.name} 不可用`, {
                            reason: field.reason
                        });
                    }
                    
                    // 最后一个字段处理完成
                    if (index === testFields.length - 1) {
                        setTimeout(() => {
                            const duration = window.mdacLogger.endPerformance('enhancedFormFill');
                            window.mdacLogger.info('FORM', `表单填充完成，总耗时: ${duration}ms`);
                        }, 100);
                    }
                }, index * 150);
            });
            
        }, 1000);
    }
    
    // 5. 测试错误处理日志
    console.log('\n❌ 测试错误处理日志:');
    if (window.mdacLogger) {
        setTimeout(() => {
            window.mdacLogger.error('NETWORK', '网络请求失败', {
                url: 'https://api.example.com/data',
                status: 500,
                error: 'Internal Server Error'
            });
            
            window.mdacLogger.error('VALIDATOR', '数据验证失败', {
                field: 'email',
                value: 'invalid-email',
                rule: 'email format'
            });
            
            window.mdacLogger.error('SYSTEM', '系统异常', {
                error: 'TypeError: Cannot read property of undefined',
                stack: 'at function1() at function2()'
            });
            
        }, 2000);
    }
    
    // 6. 测试用户操作日志
    console.log('\n👤 测试用户操作日志:');
    if (window.mdacLogger) {
        setTimeout(() => {
            window.mdacLogger.info('USER', '用户上传图片', {
                fileName: 'passport.jpg',
                fileSize: '2.5MB',
                fileType: 'image/jpeg'
            });
            
            window.mdacLogger.info('USER', '用户点击填充按钮');
            window.mdacLogger.info('USER', '用户切换调试控制台显示状态');
            window.mdacLogger.info('USER', '用户导出日志文件');
            
        }, 2500);
    }
    
    // 7. 测试日志导出功能
    console.log('\n💾 测试日志导出功能:');
    if (window.mdacLogger) {
        setTimeout(() => {
            console.log('测试文本格式导出:');
            const textLogs = window.mdacLogger.exportLogs('text');
            console.log('导出的文本日志长度:', textLogs.length);
            
            console.log('测试JSON格式导出:');
            const jsonLogs = window.mdacLogger.exportLogs('json');
            console.log('导出的JSON日志长度:', jsonLogs.length);
            
            // 注意：实际下载功能需要用户交互，这里只测试导出内容生成
            console.log('💡 提示：实际下载功能需要用户点击调试控制台中的导出按钮');
            
        }, 3000);
    }
    
    // 8. 性能测试
    console.log('\n⚡ 性能测试:');
    if (window.mdacLogger) {
        setTimeout(() => {
            const startTime = performance.now();
            
            // 批量生成日志
            for (let i = 0; i < 100; i++) {
                window.mdacLogger.debug('PERFORMANCE', `性能测试日志 ${i}`, { index: i });
            }
            
            const endTime = performance.now();
            const duration = endTime - startTime;
            
            window.mdacLogger.info('SYSTEM', `批量日志性能测试完成`, {
                logCount: 100,
                duration: `${duration.toFixed(2)}ms`,
                avgPerLog: `${(duration / 100).toFixed(2)}ms`
            });
            
            console.log(`批量生成100条日志耗时: ${duration.toFixed(2)}ms`);
            
        }, 3500);
    }
    
    // 9. 最终报告
    setTimeout(() => {
        console.log('\n📊 日志系统测试完成报告:');
        
        const testResults = {
            loggerLoaded: typeof window.mdacLogger !== 'undefined',
            debugConsoleLoaded: typeof window.mdacDebugConsole !== 'undefined',
            basicLogging: true,
            performanceMonitoring: true,
            filtering: true,
            export: true,
            errorHandling: true
        };
        
        const passedTests = Object.values(testResults).filter(result => result).length;
        const totalTests = Object.keys(testResults).length;
        
        console.log(`测试通过率: ${passedTests}/${totalTests} (${((passedTests/totalTests)*100).toFixed(1)}%)`);
        console.log('详细结果:', testResults);
        
        if (passedTests === totalTests) {
            console.log('🎉 所有测试通过！日志系统运行正常。');
        } else {
            console.log('⚠️ 部分测试未通过，请检查相关功能。');
        }
        
        // 显示最终统计
        if (window.mdacLogger) {
            const finalStats = window.mdacLogger.getStats();
            console.log('\n📈 最终日志统计:', finalStats);
        }
        
    }, 4000);
}

// 自动运行测试（如果在浏览器环境中）
if (typeof window !== 'undefined' && document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(testLoggingSystem, 2000);
    });
} else if (typeof window !== 'undefined') {
    setTimeout(testLoggingSystem, 2000);
}

// 导出测试函数（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { testLoggingSystem };
}
