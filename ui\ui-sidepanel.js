/**
 * MDAC AI智能填充工具 - 侧边栏版本
 * 处理扩展侧边栏的用户界面和交互逻辑
 */

class MDACAssistantSidePanel {
    constructor() {
        console.log('🚀 MDACAssistantSidePanel 构造函数开始执行');
        this.currentTab = null;
        this.isMDACPage = false;
        this.aiStatus = 'ready';
        this.parsedData = null;
        this.supplementData = null; // 持久化的补充信息
        this.mergedData = null; // 合并后的完整数据
        this.dataPreviewManager = null;
        this.errorRecoveryManager = null;
        this.fillMonitor = null;
        this.confidenceEvaluator = null;
        this.progressVisualizer = null;
        this.connectionCheckInterval = null;

        // 自动解析功能相关属性
        this.autoParseTimeouts = {
            personal: null,
            travel: null
        };
        this.autoParseSettings = {
            personal: true,
            travel: true,
            delay: 3000 // 3秒延迟
        };

        console.log('🚀 开始初始化侧边栏');
        this.initializeSidePanel();
        console.log('🚀 MDACAssistantSidePanel 构造函数完成');
    }

    /**
     * 侧边栏特定初始化
     */
    async initializeSidePanel() {
        console.log('🚀 MDAC AI侧边栏正在初始化...');
        
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.init());
        } else {
            await this.init();
        }
        
        // 侧边栏特定的事件监听
        this.setupSidePanelEvents();
        
        // 开始定期检查连接状态
        this.startConnectionMonitoring();
    }

    /**
     * 初始化主要功能
     */
    async init() {
        console.log('🔧 init 方法开始执行');
        console.log('🔧 检查 callGeminiAPI 方法:', typeof this.callGeminiAPI);
        
        await this.getCurrentTab();
        await this.detectMDACPage();
        this.setupEventListeners();
        this.initializeEnhancedTools();
        await this.loadUserSettings();
        this.initializeDataPreviewManager();
        this.initializeErrorRecoveryManager();
        this.initializeFillMonitor();
        this.initializeConfidenceEvaluator();
        this.initializeProgressVisualizer();
        this.initializeSupplementInput();
        await this.initializeCityViewer(); // 初始化城市查看器
        this.updateUI();
        await this.testAIConnection();
        
        console.log('🔧 init 方法完成，检查 callGeminiAPI 方法:', typeof this.callGeminiAPI);
    }

    /**
     * 设置侧边栏特定事件
     */
    setupSidePanelEvents() {
        // 监听窗口大小变化
        window.addEventListener('resize', () => this.handleResize());
        
        // 监听页面可见性变化
        document.addEventListener('visibilitychange', () => this.handleVisibilityChange());
        
        // 监听侧边栏关闭事件
        window.addEventListener('beforeunload', () => this.handleSidePanelClose());
        
        // 监听标签页切换
        chrome.tabs.onActivated.addListener(() => this.handleTabChange());
        chrome.tabs.onUpdated.addListener(() => this.handleTabChange());
    }

    /**
     * 开始连接监控
     */
    startConnectionMonitoring() {
        // 立即检查一次
        this.checkPageConnection();
        
        // 每5秒检查一次连接状态
        this.connectionCheckInterval = setInterval(() => {
            this.checkPageConnection();
        }, 5000);
    }

    /**
     * 停止连接监控
     */
    stopConnectionMonitoring() {
        if (this.connectionCheckInterval) {
            clearInterval(this.connectionCheckInterval);
            this.connectionCheckInterval = null;
        }
    }

    /**
     * 处理窗口大小变化
     */
    handleResize() {
        // 调整布局以适应新的窗口大小
        this.adjustLayout();
    }

    /**
     * 处理可见性变化
     */
    handleVisibilityChange() {
        if (document.hidden) {
            console.log('📱 侧边栏已隐藏');
            this.stopConnectionMonitoring();
        } else {
            console.log('📱 侧边栏已显示');
            // 重新开始监控
            this.startConnectionMonitoring();
        }
    }

    /**
     * 处理侧边栏关闭
     */
    handleSidePanelClose() {
        console.log('📱 侧边栏正在关闭');
        // 保存当前状态
        this.saveCurrentState();
        // 停止监控
        this.stopConnectionMonitoring();
    }

    /**
     * 初始化增强工具
     */
    initializeEnhancedTools() {
        try {
            // 初始化MDAC验证器
            if (typeof MDACValidator !== 'undefined') {
                this.validator = new MDACValidator();
                this.mdacValidator = this.validator; // 保持兼容性
                console.log('✅ MDAC验证器初始化成功');

                // 确保数据加载完成
                this.validator.ensureDataLoaded().then(() => {
                    console.log('✅ MDAC验证器数据加载完成');
                    // 数据加载完成后初始化依赖组件
                    this.onValidatorDataLoaded();
                }).catch(error => {
                    console.error('❌ MDAC验证器数据加载失败:', error);
                });
            } else {
                console.warn('⚠️ MDACValidator未找到，将使用基础验证');
            }

            // 初始化增强表单填充器
            if (typeof EnhancedFormFiller !== 'undefined') {
                this.enhancedFormFiller = new EnhancedFormFiller();
                console.log('✅ 增强表单填充器初始化成功');
            } else {
                console.warn('⚠️ EnhancedFormFiller未找到，将使用基础填充');
            }

            // 初始化Google Maps集成 - 使用延迟加载避免时序问题
            this.initializeGoogleMaps();

            console.log('✅ 增强工具初始化完成');
        } catch (error) {
            console.error('❌ 增强工具初始化失败:', error);
            this.showMessage('增强工具初始化失败: ' + error.message, 'error');
        }
    }

    /**
     * 验证器数据加载完成后的回调
     */
    onValidatorDataLoaded() {
        console.log('🔄 验证器数据加载完成，初始化依赖组件...');

        // 初始化旅行信息字段
        this.initializeTravelInfoFields();

        // 重新初始化城市查看器的州属过滤器
        if (document.getElementById('stateFilter')) {
            this.initializeStateFilter();
        }

        console.log('✅ 依赖组件初始化完成');
    }

    /**
     * 初始化Google Maps集成（延迟加载）
     */
    initializeGoogleMaps() {
        // 使用更长的延迟确保所有脚本都已加载，并增加重试机制
        let retryCount = 0;
        const maxRetries = 5;

        const tryInitialize = () => {
            if (typeof GoogleMapsIntegration !== 'undefined') {
                const apiKey = window.MDAC_AI_CONFIG?.GEMINI_CONFIG?.DEFAULT_API_KEY ||
                              (typeof GEMINI_CONFIG !== 'undefined' ? GEMINI_CONFIG.DEFAULT_API_KEY : null);
                if (apiKey) {
                    try {
                        this.googleMaps = new GoogleMapsIntegration(apiKey);
                        console.log('✅ Google Maps集成初始化成功');
                    } catch (error) {
                        console.error('❌ Google Maps集成初始化失败:', error);
                        console.warn('⚠️ 地址标准化功能不可用');
                    }
                } else {
                    console.warn('⚠️ API密钥未找到，Google Maps功能不可用');
                }
            } else {
                retryCount++;
                if (retryCount < maxRetries) {
                    console.log(`🔄 GoogleMapsIntegration未找到，重试 ${retryCount}/${maxRetries}`);
                    setTimeout(tryInitialize, 200 * retryCount); // 递增延迟
                } else {
                    console.warn('⚠️ GoogleMapsIntegration未找到，地址标准化功能不可用');
                }
            }
        };

        setTimeout(tryInitialize, 200);
    }

    /**
     * 处理标签页变化
     */
    async handleTabChange() {
        await this.getCurrentTab();
        await this.detectMDACPage();
        this.checkPageConnection();
    }

    /**
     * 调整布局
     */
    adjustLayout() {
        const container = document.querySelector('.sidepanel-container');
        if (container) {
            const width = window.innerWidth;
            if (width < 350) {
                container.classList.add('compact-mode');
            } else {
                container.classList.remove('compact-mode');
            }
        }
    }

    /**
     * 检查与页面的连接状态
     */
    async checkPageConnection() {
        try {
            const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
            const activeTab = tabs[0];
            
            if (activeTab && activeTab.url && activeTab.url.includes('imigresen-online.imi.gov.my')) {
                this.updateConnectionStatus('connected');
                this.isMDACPage = true;
            } else {
                this.updateConnectionStatus('disconnected');
                this.isMDACPage = false;
            }
            
            // 更新UI状态
            this.updateUI();
        } catch (error) {
            console.error('检查页面连接失败:', error);
            this.updateConnectionStatus('error');
        }
    }

    /**
     * 更新连接状态显示
     */
    updateConnectionStatus(status) {
        const statusElement = document.querySelector('#connectionStatus');
        if (statusElement) {
            statusElement.className = `connection-status ${status}`;
            statusElement.textContent = this.getConnectionStatusText(status);
        }
    }

    /**
     * 获取连接状态文本
     */
    getConnectionStatusText(status) {
        const statusTexts = {
            connected: '🟢 已连接到MDAC网站',
            disconnected: '🟡 请打开MDAC网站使用完整功能',
            error: '🔴 连接检测异常'
        };
        return statusTexts[status] || '🟡 状态未知';
    }

    /**
     * 保存当前状态
     */
    saveCurrentState() {
        try {
            const state = {
                parsedData: this.parsedData,
                supplementData: this.supplementData,
                mergedData: this.mergedData,
                timestamp: Date.now()
            };
            
            chrome.storage.local.set({ 'mdac_sidepanel_state': state }, () => {
                console.log('侧边栏状态已保存');
            });
        } catch (error) {
            console.error('保存状态失败:', error);
        }
    }

    /**
     * 恢复保存的状态
     */
    async restoreSavedState() {
        try {
            const result = await chrome.storage.local.get(['mdac_sidepanel_state']);
            const state = result.mdac_sidepanel_state;
            
            if (state && state.timestamp) {
                // 检查状态是否过期（24小时）
                const now = Date.now();
                const maxAge = 24 * 60 * 60 * 1000; // 24小时
                
                if (now - state.timestamp < maxAge) {
                    this.parsedData = state.parsedData;
                    this.supplementData = state.supplementData;
                    this.mergedData = state.mergedData;
                    console.log('已恢复侧边栏状态');
                    return true;
                }
            }
        } catch (error) {
            console.error('恢复状态失败:', error);
        }
        return false;
    }

    /**
     * 获取当前标签页
     */
    async getCurrentTab() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            this.currentTab = tab;
        } catch (error) {
            console.error('获取当前标签页失败:', error);
        }
    }

    /**
     * 检测是否为MDAC页面
     */
    async detectMDACPage() {
        if (!this.currentTab || !this.currentTab.url) {
            this.isMDACPage = false;
            return;
        }

        this.isMDACPage = this.currentTab.url.includes('imigresen-online.imi.gov.my');
        
        if (this.isMDACPage) {
            console.log('✅ 检测到MDAC网站');
            this.updateDetectionStatus('detected', '✅ 已检测到MDAC网站');
        } else {
            console.log('⚠️ 当前不在MDAC网站');
            this.updateDetectionStatus('not-detected', '⚠️ 请打开MDAC网站');
        }
    }

    /**
     * 更新检测状态显示
     */
    updateDetectionStatus(status, message) {
        const detectionElement = document.querySelector('#detectionStatus');
        if (detectionElement) {
            const textElement = detectionElement.querySelector('.text');
            if (textElement) {
                textElement.textContent = message;
            }
            
            detectionElement.className = `detection-status ${status}`;
        }
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 新的图片上传按钮
        document.getElementById('imageUploadBtn')?.addEventListener('click', () => this.triggerImageUpload());
        document.getElementById('imageInput')?.addEventListener('change', (e) => this.handleImageUpload(e));

        // 工具栏按钮
        document.getElementById('clearAllBtn')?.addEventListener('click', () => this.clearAllData());
        document.getElementById('previewBtn')?.addEventListener('click', () => this.previewAllData());

        // 城市查看器按钮
        document.getElementById('cityViewerBtn')?.addEventListener('click', () => this.toggleCityViewer());
        document.getElementById('closeViewerBtn')?.addEventListener('click', () => this.closeCityViewer());
        
        // 城市查看器内部控件
        document.getElementById('citySearchInput')?.addEventListener('input', (e) => this.handleCitySearch(e));
        document.getElementById('citySearchBtn')?.addEventListener('click', () => this.performCitySearch());
        document.getElementById('stateFilter')?.addEventListener('change', (e) => this.handleStateFilter(e));
        document.getElementById('showPopularBtn')?.addEventListener('click', () => this.showPopularDestinations());
        document.getElementById('listViewBtn')?.addEventListener('click', () => this.switchToListView());
        document.getElementById('gridViewBtn')?.addEventListener('click', () => this.switchToGridView());

        // 个人信息AI解析
        document.getElementById('parsePersonalBtn')?.addEventListener('click', (event) => {
            event.preventDefault();
            this.parsePersonalInfo().catch(error => {
                console.error('解析个人信息时出错:', error);
                this.showMessage('解析失败: ' + error.message, 'error');
            });
        });

        // 旅行信息AI解析
        document.getElementById('parseTravelBtn')?.addEventListener('click', () => this.parseTravelInfo());

        // 旧的解析按钮（保持向后兼容）
        document.getElementById('parseContentBtn')?.addEventListener('click', () => this.parseContent());

        // 州属和城市级联选择
        document.getElementById('state')?.addEventListener('change', (e) => this.handleStateChange(e));
        document.getElementById('city')?.addEventListener('change', (e) => this.handleCityChange(e));
        document.getElementById('postcode')?.addEventListener('input', (e) => this.handlePostcodeInput(e));
        document.getElementById('clearContentBtn')?.addEventListener('click', () => this.clearContent());

        // 补充信息相关
        document.getElementById('supplementInput')?.addEventListener('input', () => this.handleSupplementInput());
        document.getElementById('clearSupplementBtn')?.addEventListener('click', () => this.clearSupplementData());
        document.getElementById('previewMergedDataBtn')?.addEventListener('click', () => this.previewMergedData());

        // 自动解析功能事件监听器
        this.setupAutoParseListeners();

        // 表单填充
        document.getElementById('fillFormBtn')?.addEventListener('click', () => this.fillForm());
        document.getElementById('quickFillBtn')?.addEventListener('click', () => this.quickFill());
        document.getElementById('updateToMDACBtn')?.addEventListener('click', () => this.updateToMDAC());

        // 其他功能
        document.getElementById('openFormBtn')?.addEventListener('click', () => this.openFormEditor());
        document.getElementById('aiSettingsBtn')?.addEventListener('click', () => this.openAISettings());
        document.getElementById('helpBtn')?.addEventListener('click', () => this.showHelp());
        document.getElementById('saveBtn')?.addEventListener('click', () => this.saveData());
        document.getElementById('clearBtn')?.addEventListener('click', () => this.clearAllData());
        document.getElementById('settingsBtn')?.addEventListener('click', () => this.showSettings());

        // 模态框
        document.getElementById('modalClose')?.addEventListener('click', () => this.closeModal());
        document.getElementById('modalCancel')?.addEventListener('click', () => this.closeModal());
        document.getElementById('modalConfirm')?.addEventListener('click', () => this.handleModalConfirm());

        // 预设信息编辑
        document.getElementById('editPresetBtn')?.addEventListener('click', () => this.editPresetInfo());
    }

    /**
     * 加载用户设置
     */
    async loadUserSettings() {
        try {
            const result = await chrome.storage.sync.get(['mdacSettings']);
            this.userSettings = result.mdacSettings || {};
            console.log('用户设置已加载');
        } catch (error) {
            console.error('加载用户设置失败:', error);
            this.userSettings = {};
        }
    }

    /**
     * 初始化数据预览管理器
     */
    initializeDataPreviewManager() {
        try {
            this.dataPreviewManager = new DataPreviewManager();
            console.log('数据预览管理器初始化成功');
        } catch (error) {
            console.error('数据预览管理器初始化失败:', error);
        }
    }

    /**
     * 初始化错误恢复管理器
     */
    initializeErrorRecoveryManager() {
        try {
            this.errorRecoveryManager = new ErrorRecoveryManager();
            console.log('错误恢复管理器初始化成功');
        } catch (error) {
            console.error('错误恢复管理器初始化失败:', error);
        }
    }

    /**
     * 初始化填充监控器
     */
    initializeFillMonitor() {
        try {
            this.fillMonitor = new FillMonitor();
            console.log('填充监控器初始化成功');
        } catch (error) {
            console.error('填充监控器初始化失败:', error);
        }
    }

    /**
     * 初始化置信度评估器
     */
    initializeConfidenceEvaluator() {
        try {
            this.confidenceEvaluator = new ConfidenceEvaluator();
            console.log('✅ 置信度评估器初始化完成');
        } catch (error) {
            console.error('❌ 置信度评估器初始化失败:', error);
        }
    }

    /**
     * 初始化进度可视化器
     */
    initializeProgressVisualizer() {
        try {
            this.progressVisualizer = new ProgressVisualizer();
            console.log('✅ 进度可视化器初始化完成');
        } catch (error) {
            console.error('❌ 进度可视化器初始化失败:', error);
        }
    }

    /**
     * 初始化补充信息输入
     */
    async initializeSupplementInput() {
        try {
            // 加载保存的补充信息
            const result = await chrome.storage.local.get(['mdac_supplement_data']);
            if (result.mdac_supplement_data) {
                this.supplementData = result.mdac_supplement_data;
                
                // 填充到输入框
                const supplementInput = document.getElementById('supplementInput');
                if (supplementInput && this.supplementData.rawText) {
                    supplementInput.value = this.supplementData.rawText;
                }
                
                // 更新状态显示
                this.updateSupplementStatus();
            }
            
            console.log('补充信息输入初始化成功');
        } catch (error) {
            console.error('补充信息输入初始化失败:', error);
        }
    }

    /**
     * 更新UI状态
     */
    updateUI() {
        // 更新AI状态
        this.updateAIStatus();
        
        // 更新按钮状态
        this.updateButtonStates();
        
        // 更新功能可用性
        this.updateFeatureAvailability();
    }

    /**
     * 更新AI状态显示
     */
    updateAIStatus() {
        const statusElement = document.querySelector('#aiStatus');
        if (statusElement) {
            const statusDot = statusElement.querySelector('.status-dot');
            const statusText = statusElement.querySelector('.status-text');
            
            if (statusDot && statusText) {
                switch (this.aiStatus) {
                    case 'ready':
                        statusDot.style.background = '#4caf50';
                        statusText.textContent = 'AI就绪';
                        break;
                    case 'working':
                        statusDot.style.background = '#ff9800';
                        statusText.textContent = 'AI工作中';
                        break;
                    case 'error':
                        statusDot.style.background = '#f44336';
                        statusText.textContent = 'AI异常';
                        break;
                    default:
                        statusDot.style.background = '#9e9e9e';
                        statusText.textContent = '状态未知';
                }
            }
        }
    }

    /**
     * 更新按钮状态
     */
    updateButtonStates() {
        const parseBtn = document.getElementById('parseContentBtn');
        const fillBtn = document.getElementById('fillFormBtn');
        const quickFillBtn = document.getElementById('quickFillBtn');
        
        // 根据页面状态和数据状态更新按钮
        if (parseBtn) {
            parseBtn.disabled = this.aiStatus === 'working';
        }
        
        if (fillBtn) {
            fillBtn.disabled = !this.isMDACPage || (!this.parsedData && !this.supplementData);
        }
        
        if (quickFillBtn) {
            quickFillBtn.disabled = !this.isMDACPage || (!this.parsedData && !this.supplementData);
        }
    }

    /**
     * 更新功能可用性
     */
    updateFeatureAvailability() {
        const mainContent = document.querySelector('#mainContent');
        if (mainContent) {
            if (this.isMDACPage) {
                mainContent.classList.remove('disabled');
            } else {
                // 不完全禁用，但显示提示
                const quickActions = document.querySelector('.quick-actions');
                if (quickActions) {
                    const buttons = quickActions.querySelectorAll('button');
                    buttons.forEach(btn => {
                        if (btn.id === 'quickFillBtn') {
                            btn.disabled = true;
                            btn.title = '请先打开MDAC网站';
                        }
                    });
                }
            }
        }
    }

    /**
     * 测试AI连接
     */
    async testAIConnection() {
        this.aiStatus = 'working';
        this.updateAIStatus();

        try {
            const response = await chrome.runtime.sendMessage({
                action: 'test-ai-connection'
            });

            if (response && response.success) {
                this.aiStatus = 'ready';
                console.log('✅ AI连接测试成功');
            } else {
                throw new Error(response?.error || 'AI连接测试失败');
            }
        } catch (error) {
            this.aiStatus = 'error';
            console.error('❌ AI连接测试失败:', error);
        }

        this.updateAIStatus();
    }

    /**
     * 调用Gemini AI API
     * @param {string} prompt - AI提示词
     * @param {string} context - 上下文信息
     * @returns {Promise<string>} AI响应结果
     */
    async callGeminiAPI(prompt, context = '') {
        try {
            const apiKey = window.MDAC_AI_CONFIG?.GEMINI_CONFIG?.DEFAULT_API_KEY ||
                          GEMINI_CONFIG?.DEFAULT_API_KEY;
            
            if (!apiKey) {
                throw new Error('API密钥未配置');
            }

            const model = window.MDAC_AI_CONFIG?.GEMINI_CONFIG?.DEFAULT_MODEL ||
                         GEMINI_CONFIG?.DEFAULT_MODEL ||
                         'gemini-2.5-flash-lite-preview-06-17';

            const url = `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${apiKey}`;

            const requestBody = {
                contents: [{
                    parts: [{
                        text: context ? `${context}\n\n${prompt}` : prompt
                    }]
                }],
                generationConfig: window.MDAC_AI_CONFIG?.GEMINI_CONFIG?.DEFAULT_GENERATION_CONFIG ||
                                 GEMINI_CONFIG?.DEFAULT_GENERATION_CONFIG,
                safetySettings: window.MDAC_AI_CONFIG?.GEMINI_CONFIG?.SAFETY_SETTINGS ||
                               GEMINI_CONFIG?.SAFETY_SETTINGS
            };

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(`API请求失败: ${response.status} ${response.statusText}. ${errorData.error?.message || ''}`);
            }

            const data = await response.json();
            
            if (!data.candidates || data.candidates.length === 0) {
                throw new Error('AI未返回有效响应');
            }

            const candidate = data.candidates[0];
            if (candidate.finishReason === 'SAFETY') {
                throw new Error('AI响应被安全过滤器阻止');
            }

            const content = candidate.content?.parts?.[0]?.text;
            if (!content) {
                throw new Error('AI响应内容为空');
            }

            return content.trim();

        } catch (error) {
            console.error('❌ Gemini API调用失败:', error);
            throw error;
        }
    }

    /**
     * 解析内容
     */
    async parseContent() {
        const contentInput = document.getElementById('contentInput');
        const content = contentInput?.value?.trim();

        if (!content) {
            this.showMessage('请先输入要解析的内容', 'warning');
            return;
        }

        this.showParsingStatus(true);
        this.aiStatus = 'working';
        this.updateAIStatus();

        try {
            const response = await chrome.runtime.sendMessage({
                action: 'callGeminiAI',
                prompt: `请从以下内容中提取MDAC表单所需的个人信息：\n\n${content}`,
                context: 'MDAC表单填充'
            });

            if (response && response.success) {
                this.parsedData = this.parseAIResponse(response.data);
                this.displayParseResults();
                this.showMessage('内容解析完成', 'success');
            } else {
                throw new Error(response?.error || 'AI解析失败');
            }
        } catch (error) {
            console.error('内容解析失败:', error);
            this.showMessage('内容解析失败: ' + error.message, 'error');
        }

        this.showParsingStatus(false);
        this.aiStatus = 'ready';
        this.updateAIStatus();
    }

    /**
     * 处理图片上传
     */
    triggerImageUpload() {
        document.getElementById('imageInput')?.click();
    }

    /**
     * 处理图片上传事件
     */
    async handleImageUpload(event) {
        const file = event.target.files[0];
        if (!file) return;

        if (!file.type.startsWith('image/')) {
            this.showMessage('请选择有效的图片文件', 'error');
            return;
        }

        this.showParsingStatus(true);
        this.aiStatus = 'working';
        this.updateAIStatus();

        try {
            const base64 = await this.fileToBase64(file);
            const response = await chrome.runtime.sendMessage({
                action: 'callGeminiVision',
                image: base64,
                prompt: '请提取图片中的文字信息，特别是个人身份信息、护照信息、联系方式等'
            });

            if (response && response.success) {
                // 将提取的文字填入内容输入框
                const contentInput = document.getElementById('contentInput');
                if (contentInput) {
                    contentInput.value = response.data;
                }
                this.showMessage('图片文字提取成功', 'success');
            } else {
                throw new Error(response?.error || '图片文字提取失败');
            }
        } catch (error) {
            console.error('图片处理失败:', error);
            this.showMessage('图片处理失败: ' + error.message, 'error');
        }

        this.showParsingStatus(false);
        this.aiStatus = 'ready';
        this.updateAIStatus();
    }

    /**
     * 文件转Base64
     */
    fileToBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => {
                const base64 = reader.result.split(',')[1];
                resolve(base64);
            };
            reader.onerror = reject;
            reader.readAsDataURL(file);
        });
    }

    /**
     * 处理补充信息输入
     */
    async handleSupplementInput() {
        const supplementInput = document.getElementById('supplementInput');
        const rawText = supplementInput?.value || '';

        // 解析补充信息
        const parsedSupplement = this.parseSupplementText(rawText);

        // 保存到本地存储
        this.supplementData = {
            rawText: rawText,
            parsedFields: parsedSupplement,
            timestamp: Date.now()
        };

        try {
            await chrome.storage.local.set({ 'mdac_supplement_data': this.supplementData });
            this.updateSupplementStatus();
        } catch (error) {
            console.error('保存补充信息失败:', error);
        }
    }

    /**
     * 解析补充信息文本
     */
    parseSupplementText(text) {
        const fields = {};
        const lines = text.split('\n');

        const fieldMappings = {
            '邮箱': 'email',
            '电话': 'mobileNo',
            '国家代码': 'countryCode',
            '航班号': 'flightNo',
            '到达日期': 'arrivalDate',
            '离开日期': 'departureDate',
            '住宿类型': 'accommodation',
            '地址': 'address',
            '州': 'state',
            '邮编': 'postcode',
            '城市': 'city',
            '旅行方式': 'modeOfTravel',
            '最后港口': 'lastPort'
        };

        lines.forEach(line => {
            const colonIndex = line.indexOf('：');
            if (colonIndex > 0) {
                const key = line.substring(0, colonIndex).trim();
                const value = line.substring(colonIndex + 1).trim();

                if (fieldMappings[key] && value) {
                    fields[fieldMappings[key]] = value;
                }
            }
        });

        return fields;
    }

    /**
     * 更新补充信息状态
     */
    updateSupplementStatus() {
        const statusElement = document.querySelector('#supplementStatus .status-text');
        if (statusElement && this.supplementData) {
            const fieldCount = Object.keys(this.supplementData.parsedFields || {}).length;
            const charCount = (this.supplementData.rawText || '').length;

            if (fieldCount > 0) {
                statusElement.textContent = `已保存${fieldCount}个字段，${charCount}个字符`;
            } else if (charCount > 0) {
                statusElement.textContent = `已保存${charCount}个字符，等待解析`;
            } else {
                statusElement.textContent = '暂无保存的补充信息';
            }
        }
    }

    /**
     * 清空补充信息
     */
    async clearSupplementData() {
        try {
            await chrome.storage.local.remove(['mdac_supplement_data']);
            this.supplementData = null;

            const supplementInput = document.getElementById('supplementInput');
            if (supplementInput) {
                supplementInput.value = '';
            }

            this.updateSupplementStatus();
            this.showMessage('补充信息已清空', 'success');
        } catch (error) {
            console.error('清空补充信息失败:', error);
            this.showMessage('清空失败', 'error');
        }
    }

    /**
     * 预览合并数据
     */
    previewMergedData() {
        if (!this.parsedData && !this.supplementData) {
            this.showMessage('暂无数据可预览', 'warning');
            return;
        }

        this.mergedData = this.mergeDataSources();

        if (this.dataPreviewManager) {
            this.dataPreviewManager.showMergedDataPreview(
                this.parsedData,
                this.supplementData?.parsedFields,
                this.mergedData
            );
        }
    }

    /**
     * 合并数据源
     */
    mergeDataSources() {
        const merged = {};

        // 先添加补充信息数据
        if (this.supplementData && this.supplementData.parsedFields) {
            Object.assign(merged, this.supplementData.parsedFields);
        }

        // AI解析数据优先级更高，会覆盖补充信息中的同名字段
        if (this.parsedData) {
            Object.assign(merged, this.parsedData);
        }

        return merged;
    }

    /**
     * 填充表单
     */
    async fillForm() {
        if (!this.isMDACPage) {
            this.showMessage('请先打开MDAC网站', 'warning');
            return;
        }

        // 如果有多个数据源，显示预览
        if (this.parsedData && this.supplementData) {
            this.previewMergedData();
            return;
        }

        // 单一数据源直接填充
        const dataToFill = this.parsedData || this.supplementData?.parsedFields;
        if (!dataToFill) {
            this.showMessage('暂无数据可填充', 'warning');
            return;
        }

        await this.performFormFill(dataToFill);
    }

    /**
     * 执行表单填充
     */
    async performFormFill(data) {
        let sessionId = null;

        try {
            // 准备字段列表用于进度可视化
            const fieldList = Object.entries(data).map(([key, value]) => ({
                key,
                label: this.getFieldLabel(key),
                value: value
            }));

            // 启动进度可视化
            if (this.progressVisualizer) {
                this.progressVisualizer.startProgress({
                    totalFields: fieldList.length,
                    fieldList: fieldList,
                    title: 'MDAC表单填充进度',
                    showInSidepanel: true
                });
            }

            // 启动填充监控会话
            if (this.fillMonitor) {
                sessionId = this.fillMonitor.startFillSession(
                    data,
                    {}, // 字段映射将在content script中获取
                    (progress) => this.updateFillProgress(progress)
                );
                console.log(`🚀 开始填充监控会话: ${sessionId}`);
            }

            // 发送填充请求到content script
            const response = await chrome.tabs.sendMessage(this.currentTab.id, {
                action: 'fillForm',
                data: data,
                sessionId: sessionId, // 传递会话ID
                fieldList: fieldList  // 传递字段列表用于进度更新
            });

            if (response && response.success) {
                this.showMessage('表单填充成功', 'success');

                // 结束填充会话
                if (this.fillMonitor && sessionId) {
                    this.fillMonitor.endFillSession('success');
                }

                // 触发首次填充成功引导
                if (this.userGuideManager) {
                    this.userGuideManager.triggerScenarioGuide('firstFillSuccess');
                }
            } else {
                throw new Error(response?.error || '表单填充失败');
            }
        } catch (error) {
            console.error('表单填充失败:', error);

            // 结束填充会话（失败状态）
            if (this.fillMonitor && sessionId) {
                this.fillMonitor.endFillSession('failed');
            }

            // 使用错误恢复管理器处理错误
            if (this.errorRecoveryManager) {
                this.errorRecoveryManager.handleError(error, {
                    operation: 'performFormFill',
                    data: data,
                    sessionId: sessionId
                });
            } else {
                this.showMessage('表单填充失败: ' + error.message, 'error');
            }
        }
    }

    /**
     * 更新填充进度
     * @param {Object} progress 进度信息
     */
    updateFillProgress(progress) {
        console.log('📊 填充进度更新:', progress);

        // 更新进度显示
        const progressElement = document.getElementById('fillProgress');
        if (progressElement) {
            progressElement.style.display = 'block';
            progressElement.innerHTML = `
                <div class="progress-header">
                    <span class="progress-title">表单填充进度</span>
                    <span class="progress-percentage">${progress.percentage}%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${progress.percentage}%"></div>
                </div>
                <div class="progress-details">
                    <span class="progress-stat">✅ 成功: ${progress.successful}</span>
                    <span class="progress-stat">❌ 失败: ${progress.failed}</span>
                    <span class="progress-stat">⏭️ 跳过: ${progress.skipped}</span>
                    <span class="progress-stat">⏳ 进行中: ${progress.inProgress}</span>
                </div>
            `;
        }

        // 如果填充完成，隐藏进度条
        if (progress.percentage === 100) {
            setTimeout(() => {
                if (progressElement) {
                    progressElement.style.display = 'none';
                }
            }, 3000);
        }
    }

    /**
     * 更新字段填充状态（供content script调用）
     * @param {string} fieldKey 字段键
     * @param {string} status 状态
     * @param {Object} details 详细信息
     */
    updateFieldStatus(fieldKey, status, details = {}) {
        // 更新进度可视化器
        if (this.progressVisualizer) {
            this.progressVisualizer.updateFieldStatus(fieldKey, status, details);
        }

        console.log(`📝 字段状态更新: ${fieldKey} -> ${status}`);
    }

    /**
     * 一键智能填充
     */
    async quickFill() {
        if (!this.isMDACPage) {
            this.showMessage('请先打开MDAC网站', 'warning');
            return;
        }

        // 合并所有可用数据
        const mergedData = this.mergeDataSources();
        if (Object.keys(mergedData).length === 0) {
            this.showMessage('暂无数据可填充', 'warning');
            return;
        }

        await this.performFormFill(mergedData);
    }

    /**
     * 显示消息
     */
    showMessage(message, type = 'info') {
        // 创建消息提示
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;
        messageDiv.textContent = message;
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 16px;
            border-radius: 4px;
            color: white;
            font-size: 13px;
            z-index: 10000;
            max-width: 300px;
            word-wrap: break-word;
        `;

        // 设置背景色
        switch (type) {
            case 'success':
                messageDiv.style.background = '#4caf50';
                break;
            case 'error':
                messageDiv.style.background = '#f44336';
                break;
            case 'warning':
                messageDiv.style.background = '#ff9800';
                break;
            default:
                messageDiv.style.background = '#2196f3';
        }

        document.body.appendChild(messageDiv);

        // 3秒后自动移除
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 3000);
    }

    /**
     * 显示解析状态
     */
    showParsingStatus(show) {
        const statusElement = document.getElementById('parsingStatus');
        if (statusElement) {
            statusElement.style.display = show ? 'block' : 'none';
        }
    }

    /**
     * 清空内容
     */
    clearContent() {
        const contentInput = document.getElementById('contentInput');
        if (contentInput) {
            contentInput.value = '';
        }

        this.parsedData = null;
        this.hideParseResults();
        this.showMessage('内容已清空', 'success');
    }

    /**
     * 隐藏解析结果
     */
    hideParseResults() {
        const resultsElement = document.getElementById('parseResults');
        if (resultsElement) {
            resultsElement.style.display = 'none';
        }
    }

    /**
     * 解析AI响应
     */
    parseAIResponse(response) {
        console.log('🔍 开始解析AI响应:', response);

        try {
            if (typeof response === 'string') {
                // 尝试从文本中提取结构化数据
                const extractedData = this.extractStructuredData(response);
                console.log('📊 提取的结构化数据:', extractedData);
                return extractedData;
            }
            return response;
        } catch (error) {
            console.error('解析AI响应失败:', error);
            return {};
        }
    }

    /**
     * 从文本中提取结构化数据
     */
    extractStructuredData(text) {
        console.log('🔍 开始提取结构化数据，原始文本:', text);

        try {
            // 首先尝试直接解析JSON
            const jsonMatch = text.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                const jsonStr = jsonMatch[0];
                console.log('📋 找到JSON字符串:', jsonStr);

                // 清理JSON字符串
                const cleanedJson = jsonStr
                    .replace(/```json|```/g, '') // 移除markdown标记
                    .replace(/[\u201C\u201D]/g, '"') // 替换中文引号
                    .replace(/[\u2018\u2019]/g, "'") // 替换中文单引号
                    .trim();

                const parsedData = JSON.parse(cleanedJson);
                console.log('✅ JSON解析成功:', parsedData);
                return parsedData;
            }

            // 如果没有找到JSON，尝试从文本中提取字段
            console.log('⚠️ 未找到JSON格式，尝试文本解析');
            return this.extractFromPlainText(text);

        } catch (error) {
            console.error('❌ JSON解析失败:', error);
            console.log('🔄 回退到文本解析模式');
            return this.extractFromPlainText(text);
        }
    }

    /**
     * 从纯文本中提取数据（备用方案）
     */
    extractFromPlainText(text) {
        console.log('🔍 开始纯文本解析，输入文本:', text);
        const data = {};

        // 首先尝试解析日期范围（优先处理）
        this.extractDateRange(text, data);

        // 定义字段模式
        const patterns = {
            name: /(?:姓名|name|名字)[:：]\s*([^\n\r,，]+)/i,
            passportNo: /(?:护照号|passport\s*no|护照号码)[:：]\s*([A-Z0-9]+)/i,
            dateOfBirth: /(?:出生日期|date\s*of\s*birth|生日)[:：]\s*([0-9\/\-\.]+)/i,
            nationality: /(?:国籍|nationality)[:：]\s*([^\n\r,，]+)/i,
            sex: /(?:性别|sex|gender)[:：]\s*([^\n\r,，]+)/i,
            email: /(?:邮箱|email|电子邮件)[:：]\s*([^\s\n\r,，]+@[^\s\n\r,，]+)/i,
            mobileNo: /(?:电话|手机|phone|mobile)[:：]\s*([+0-9\-\s]+)/i,
            // 单独的日期模式（如果日期范围解析失败时使用）
            arrivalDate: /(?:到达日期|arrival\s*date|入境日期|到达)[:：]\s*([0-9\/\-\.]+)/i,
            departureDate: /(?:离开日期|departure\s*date|出境日期|离开)[:：]\s*([0-9\/\-\.]+)/i,
            // 交通工具识别（增强版）
            flightNo: /(?:航班号|flight\s*no|航班|巴士|bus|火车|train)[:：]?\s*([A-Z0-9]+)/i,
            // 地址和住宿信息（增强版）
            address: /(?:地址|address|住址|入住|住宿地址)[:：]?\s*([^\n\r,，。；]+)/i,
            accommodation: /(?:住宿|accommodation|住宿类型|酒店|hotel)[:：]\s*([^\n\r,，]+)/i
        };

        // 应用模式提取数据
        for (const [field, pattern] of Object.entries(patterns)) {
            // 如果日期字段已经通过日期范围解析获得，跳过单独的日期模式
            if ((field === 'arrivalDate' || field === 'departureDate') && data[field]) {
                console.log(`⏭️ 跳过字段 ${field}，已通过日期范围解析获得: ${data[field]}`);
                continue;
            }

            const match = text.match(pattern);
            if (match && match[1]) {
                let value = match[1].trim();

                // 特殊处理不同字段类型
                if (field === 'sex') {
                    value = value.includes('男') || value.toLowerCase().includes('male') ? '1' :
                           value.includes('女') || value.toLowerCase().includes('female') ? '2' : value;
                } else if (field === 'nationality') {
                    // 简单的国籍映射
                    const nationalityMap = {
                        '中国': 'CHN', '美国': 'USA', '英国': 'GBR',
                        '新加坡': 'SGP', '马来西亚': 'MYS', '日本': 'JPN'
                    };
                    value = nationalityMap[value] || value;
                } else if (field === 'flightNo') {
                    // 交通工具号码处理
                    value = value.toUpperCase();
                    console.log(`🚌 识别到交通工具: ${value}`);
                } else if (field === 'address') {
                    // 地址信息增强处理
                    value = this.enhanceAddressInfo(value, text);
                }

                // 避免覆盖已有的更好数据
                if (!data[field] || data[field] === '') {
                    data[field] = value;
                    console.log(`📝 提取字段 ${field}: ${value}`);
                } else {
                    console.log(`⏭️ 字段 ${field} 已有值，跳过: ${data[field]}`);
                }
            }
        }

        // 特殊处理：从文本中提取更多地址信息
        this.extractAddressInfo(text, data);

        console.log('📊 文本解析结果:', data);
        return data;
    }

    /**
     * 从文本中提取日期范围
     * 支持格式：12/07/2025 - 17/07/2025, 2025-07-12~2025-07-17 等
     */
    extractDateRange(text, data) {
        console.log('📅 开始日期范围解析');

        // 日期范围模式（支持多种分隔符）
        const dateRangePatterns = [
            // DD/MM/YYYY - DD/MM/YYYY 格式
            /(\d{1,2}\/\d{1,2}\/\d{4})\s*[-~至到]\s*(\d{1,2}\/\d{1,2}\/\d{4})/g,
            // YYYY-MM-DD - YYYY-MM-DD 格式
            /(\d{4}-\d{1,2}-\d{1,2})\s*[-~至到]\s*(\d{4}-\d{1,2}-\d{1,2})/g,
            // DD-MM-YYYY - DD-MM-YYYY 格式
            /(\d{1,2}-\d{1,2}-\d{4})\s*[-~至到]\s*(\d{1,2}-\d{1,2}-\d{4})/g,
            // DD.MM.YYYY - DD.MM.YYYY 格式
            /(\d{1,2}\.\d{1,2}\.\d{4})\s*[-~至到]\s*(\d{1,2}\.\d{1,2}\.\d{4})/g
        ];

        for (const pattern of dateRangePatterns) {
            const matches = text.matchAll(pattern);
            for (const match of matches) {
                const startDate = match[1].trim();
                const endDate = match[2].trim();

                console.log(`📅 找到日期范围: ${startDate} - ${endDate}`);

                // 判断哪个是到达日期，哪个是离开日期
                // 通常第一个日期是到达日期，第二个是离开日期
                data.arrivalDate = startDate;
                data.departureDate = endDate;

                console.log(`✅ 日期范围解析成功: 到达=${startDate}, 离开=${endDate}`);
                return; // 找到第一个有效的日期范围就返回
            }
        }

        // 如果没有找到日期范围，尝试查找单独的日期
        console.log('🔍 未找到日期范围，尝试查找单独日期');

        // 查找可能的到达和离开关键词附近的日期
        const arrivalPatterns = [
            /(?:到达|入境|抵达|arrival).*?(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{4})/i,
            /(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{4}).*?(?:到达|入境|抵达|arrival)/i
        ];

        const departurePatterns = [
            /(?:离开|出境|departure|离境).*?(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{4})/i,
            /(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{4}).*?(?:离开|出境|departure|离境)/i
        ];

        // 尝试匹配到达日期
        for (const pattern of arrivalPatterns) {
            const match = text.match(pattern);
            if (match) {
                data.arrivalDate = match[1].trim();
                console.log(`📅 找到到达日期: ${data.arrivalDate}`);
                break;
            }
        }

        // 尝试匹配离开日期
        for (const pattern of departurePatterns) {
            const match = text.match(pattern);
            if (match) {
                data.departureDate = match[1].trim();
                console.log(`📅 找到离开日期: ${data.departureDate}`);
                break;
            }
        }
    }

    /**
     * 增强地址信息处理
     * 特别处理中文地名和景点名称
     */
    enhanceAddressInfo(address, fullText) {
        console.log(`🏠 增强地址信息处理: ${address}`);

        // 中文地名和景点的英文对照
        const locationMap = {
            '新山': 'Johor Bahru',
            '乐高': 'Legoland',
            '新山乐高': 'Legoland Johor Bahru',
            '吉隆坡': 'Kuala Lumpur',
            '槟城': 'Penang',
            '马六甲': 'Malacca',
            '云顶': 'Genting Highlands',
            '双子塔': 'Petronas Twin Towers'
        };

        let enhancedAddress = address;

        // 检查是否包含已知的中文地名
        for (const [chinese, english] of Object.entries(locationMap)) {
            if (address.includes(chinese)) {
                enhancedAddress = enhancedAddress.replace(chinese, english);
                console.log(`🔄 地名翻译: ${chinese} -> ${english}`);
            }
        }

        // 如果地址很短，尝试从完整文本中获取更多上下文
        if (enhancedAddress.length < 10) {
            const contextPatterns = [
                new RegExp(`([^。，,\\n]*${address.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}[^。，,\\n]*)`, 'i'),
                new RegExp(`(入住[^。，,\\n]*${address.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}[^。，,\\n]*)`, 'i')
            ];

            for (const pattern of contextPatterns) {
                const match = fullText.match(pattern);
                if (match && match[1] && match[1].length > enhancedAddress.length) {
                    enhancedAddress = match[1].trim();
                    console.log(`📍 获取地址上下文: ${enhancedAddress}`);
                    break;
                }
            }
        }

        return enhancedAddress;
    }

    /**
     * 从文本中提取额外的地址信息
     */
    extractAddressInfo(text, data) {
        console.log('🏠 提取额外地址信息');

        // 住宿相关的模式
        const accommodationPatterns = [
            /入住\s*([^，,。\n]+)/i,
            /住宿\s*([^，,。\n]+)/i,
            /酒店\s*([^，,。\n]+)/i,
            /([^，,。\n]*乐高[^，,。\n]*)/i,
            /([^，,。\n]*酒店[^，,。\n]*)/i
        ];

        for (const pattern of accommodationPatterns) {
            const match = text.match(pattern);
            if (match && match[1]) {
                const addressInfo = match[1].trim();

                // 如果还没有地址信息，或者找到的信息更详细
                if (!data.address || data.address.length < addressInfo.length) {
                    data.address = this.enhanceAddressInfo(addressInfo, text);
                    console.log(`🏨 提取住宿地址: ${data.address}`);
                }
                break;
            }
        }

        // 交通方式识别
        const transportPatterns = [
            /巴士[:：]?\s*([A-Z0-9]+)/i,
            /bus[:：]?\s*([A-Z0-9]+)/i,
            /航班[:：]?\s*([A-Z0-9]+)/i,
            /flight[:：]?\s*([A-Z0-9]+)/i
        ];

        for (const pattern of transportPatterns) {
            const match = text.match(pattern);
            if (match && match[1]) {
                const transportNo = match[1].trim().toUpperCase();

                // 根据编号格式判断交通方式
                if (transportNo.startsWith('PC') || transportNo.includes('BUS')) {
                    data.modeOfTravel = 'LAND'; // 陆路交通
                } else if (transportNo.match(/^[A-Z]{2}\d+$/)) {
                    data.modeOfTravel = 'AIR'; // 航空
                }

                data.flightNo = transportNo;
                console.log(`🚌 识别交通工具: ${transportNo}, 方式: ${data.modeOfTravel || '未确定'}`);
                break;
            }
        }
    }

    /**
     * 日期格式转换函数 - 将多种日期格式转换为 yyyy-MM-dd
     * @param {string} dateStr - 输入的日期字符串
     * @returns {string} - 转换后的日期字符串 (yyyy-MM-dd) 或原字符串（如果转换失败）
     */
    convertDateFormat(dateStr) {
        if (!dateStr || typeof dateStr !== 'string') {
            console.warn('⚠️ 日期转换：输入为空或非字符串:', dateStr);
            return dateStr;
        }

        const originalDate = dateStr.trim();
        console.log(`📅 开始日期格式转换: "${originalDate}"`);

        try {
            // 支持的日期格式模式
            const patterns = [
                // dd/MM/yyyy 格式
                {
                    regex: /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/,
                    format: 'dd/MM/yyyy'
                },
                // dd-MM-yyyy 格式
                {
                    regex: /^(\d{1,2})-(\d{1,2})-(\d{4})$/,
                    format: 'dd-MM-yyyy'
                },
                // dd.MM.yyyy 格式
                {
                    regex: /^(\d{1,2})\.(\d{1,2})\.(\d{4})$/,
                    format: 'dd.MM.yyyy'
                },
                // yyyy-MM-dd 格式（已经是目标格式）
                {
                    regex: /^(\d{4})-(\d{1,2})-(\d{1,2})$/,
                    format: 'yyyy-MM-dd',
                    isTarget: true
                },
                // yyyy/MM/dd 格式
                {
                    regex: /^(\d{4})\/(\d{1,2})\/(\d{1,2})$/,
                    format: 'yyyy/MM/dd',
                    isTarget: true
                }
            ];

            for (const pattern of patterns) {
                const match = originalDate.match(pattern.regex);
                if (match) {
                    console.log(`📋 匹配到格式: ${pattern.format}`);

                    if (pattern.isTarget) {
                        // 如果已经是目标格式或类似格式，进行标准化
                        const year = match[1];
                        const month = match[2].padStart(2, '0');
                        const day = match[3].padStart(2, '0');
                        const result = `${year}-${month}-${day}`;
                        console.log(`✅ 日期格式转换成功: "${originalDate}" → "${result}"`);
                        return result;
                    } else {
                        // dd/MM/yyyy 类型格式，需要重新排列
                        const day = match[1].padStart(2, '0');
                        const month = match[2].padStart(2, '0');
                        const year = match[3];

                        // 验证日期有效性
                        const date = new Date(year, month - 1, day);
                        if (date.getFullYear() == year &&
                            date.getMonth() == month - 1 &&
                            date.getDate() == day) {
                            const result = `${year}-${month}-${day}`;
                            console.log(`✅ 日期格式转换成功: "${originalDate}" → "${result}"`);
                            return result;
                        } else {
                            console.warn(`⚠️ 日期无效: ${originalDate}`);
                            return originalDate;
                        }
                    }
                }
            }

            // 如果没有匹配到任何模式，尝试使用Date对象解析
            console.log('🔄 尝试使用Date对象解析');
            const date = new Date(originalDate);
            if (!isNaN(date.getTime())) {
                const year = date.getFullYear();
                const month = (date.getMonth() + 1).toString().padStart(2, '0');
                const day = date.getDate().toString().padStart(2, '0');
                const result = `${year}-${month}-${day}`;
                console.log(`✅ Date对象解析成功: "${originalDate}" → "${result}"`);
                return result;
            }

            console.warn(`⚠️ 无法识别的日期格式: "${originalDate}"`);
            return originalDate;

        } catch (error) {
            console.error(`❌ 日期格式转换失败: "${originalDate}"`, error);
            return originalDate;
        }
    }

    /**
     * 显示解析结果
     */
    displayParseResults() {
        const resultsElement = document.getElementById('parseResults');
        if (resultsElement && this.parsedData) {
            resultsElement.style.display = 'block';

            // 进行置信度评估
            if (this.confidenceEvaluator) {
                const evaluation = this.confidenceEvaluator.evaluateConfidence(this.parsedData);
                this.displayConfidenceEvaluation(evaluation);
            }

            // 显示解析结果的详细内容
            this.renderParseResultsContent();
        }
    }

    /**
     * 显示置信度评估结果
     */
    displayConfidenceEvaluation(evaluation) {
        const resultsElement = document.getElementById('parseResults');
        if (!resultsElement) return;

        const visualization = this.confidenceEvaluator.createConfidenceVisualization(evaluation);

        // 创建置信度评估显示区域
        let confidenceSection = resultsElement.querySelector('.confidence-section');
        if (!confidenceSection) {
            confidenceSection = document.createElement('div');
            confidenceSection.className = 'confidence-section';
            resultsElement.insertBefore(confidenceSection, resultsElement.firstChild);
        }

        confidenceSection.innerHTML = `
            <div class="confidence-header">
                <h3>
                    <span class="confidence-icon">${visualization.overall.icon}</span>
                    AI解析置信度评估
                </h3>
                <div class="overall-confidence">
                    <span class="confidence-score" style="color: ${visualization.overall.color}">
                        ${evaluation.overall.confidence}%
                    </span>
                    <span class="confidence-level">${visualization.overall.description}</span>
                </div>
            </div>

            <div class="confidence-progress">
                <div class="progress-bar-container">
                    <div class="progress-bar" style="width: ${evaluation.overall.confidence}%; background: ${visualization.overall.color}"></div>
                </div>
            </div>

            <div class="confidence-summary">
                <div class="summary-stats">
                    <div class="stat-item high">
                        <span class="stat-count">${evaluation.summary.highConfidence}</span>
                        <span class="stat-label">高置信度</span>
                    </div>
                    <div class="stat-item medium">
                        <span class="stat-count">${evaluation.summary.mediumConfidence}</span>
                        <span class="stat-label">中等置信度</span>
                    </div>
                    <div class="stat-item low">
                        <span class="stat-count">${evaluation.summary.lowConfidence}</span>
                        <span class="stat-label">低置信度</span>
                    </div>
                    <div class="stat-item critical">
                        <span class="stat-count">${evaluation.summary.criticalConfidence}</span>
                        <span class="stat-label">极低置信度</span>
                    </div>
                </div>
            </div>

            ${evaluation.overall.issues.length > 0 ? `
                <div class="confidence-issues">
                    <h4>⚠️ 发现的问题</h4>
                    <ul class="issues-list">
                        ${evaluation.overall.issues.map(issue => `<li>${issue}</li>`).join('')}
                    </ul>
                </div>
            ` : ''}

            ${evaluation.overall.recommendations.length > 0 ? `
                <div class="confidence-recommendations">
                    <h4>💡 建议</h4>
                    <ul class="recommendations-list">
                        ${evaluation.overall.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                    </ul>
                </div>
            ` : ''}

            <div class="confidence-actions">
                <button class="confidence-btn secondary" onclick="window.mdacSidepanel.showDetailedConfidence('${evaluation.evaluationId}')">
                    查看详细分析
                </button>
                <button class="confidence-btn primary" onclick="window.mdacSidepanel.proceedWithConfidence()">
                    继续使用数据
                </button>
            </div>
        `;

        // 暴露到全局以供按钮调用
        window.mdacSidepanel = this;

        // 存储当前评估结果
        this.currentEvaluation = evaluation;

        console.log('📊 置信度评估结果已显示');
    }

    /**
     * 渲染解析结果内容
     */
    renderParseResultsContent() {
        const resultsElement = document.getElementById('parseResults');
        if (!resultsElement || !this.parsedData) return;

        // 创建或更新数据显示区域
        let dataSection = resultsElement.querySelector('.data-section');
        if (!dataSection) {
            dataSection = document.createElement('div');
            dataSection.className = 'data-section';
            resultsElement.appendChild(dataSection);
        }

        const fieldCount = Object.keys(this.parsedData).length;

        dataSection.innerHTML = `
            <div class="data-header">
                <h3>解析结果 (${fieldCount}个字段)</h3>
                <div class="data-actions">
                    <button class="data-btn" onclick="window.mdacSidepanel.editParsedData()">编辑</button>
                    <button class="data-btn" onclick="window.mdacSidepanel.exportData()">导出</button>
                </div>
            </div>
            <div class="data-fields">
                ${this.renderDataFields()}
            </div>
        `;
    }

    /**
     * 渲染数据字段
     */
    renderDataFields() {
        if (!this.parsedData) return '';

        return Object.entries(this.parsedData).map(([key, value]) => {
            const fieldEval = this.currentEvaluation?.fields[key];
            const confidenceClass = fieldEval ? `confidence-${fieldEval.level}` : '';
            const confidenceIcon = fieldEval ? fieldEval.icon : '';
            const confidenceScore = fieldEval ? `${fieldEval.confidence}%` : '';

            return `
                <div class="data-field ${confidenceClass}">
                    <div class="field-header">
                        <span class="field-label">${this.getFieldLabel(key)}</span>
                        <div class="field-confidence">
                            <span class="confidence-icon">${confidenceIcon}</span>
                            <span class="confidence-score">${confidenceScore}</span>
                        </div>
                    </div>
                    <div class="field-value">${value || '<span class="empty-value">未填写</span>'}</div>
                    ${fieldEval && fieldEval.issues.length > 0 ? `
                        <div class="field-issues">
                            ${fieldEval.issues.map(issue => `<span class="issue-tag">${issue}</span>`).join('')}
                        </div>
                    ` : ''}
                </div>
            `;
        }).join('');
    }

    /**
     * 获取字段标签
     */
    getFieldLabel(fieldKey) {
        const labels = {
            name: '姓名',
            passportNo: '护照号码',
            dateOfBirth: '出生日期',
            nationality: '国籍',
            sex: '性别',
            passportExpiry: '护照到期日',
            email: '电子邮箱',
            confirmEmail: '确认邮箱',
            countryCode: '国家代码',
            mobileNo: '手机号码',
            arrivalDate: '到达日期',
            departureDate: '离开日期',
            flightNo: '航班号',
            modeOfTravel: '旅行方式',
            lastPort: '最后港口',
            accommodation: '住宿类型',
            address: '地址',
            address2: '地址2',
            state: '州/省',
            postcode: '邮政编码',
            city: '城市'
        };
        return labels[fieldKey] || fieldKey;
    }

    /**
     * 显示详细置信度分析
     */
    showDetailedConfidence(evaluationId) {
        if (!this.currentEvaluation) return;

        const modal = document.createElement('div');
        modal.className = 'confidence-detail-modal';
        modal.innerHTML = `
            <div class="modal-overlay" onclick="this.parentElement.remove()"></div>
            <div class="modal-container">
                <div class="modal-header">
                    <h3>详细置信度分析</h3>
                    <button class="modal-close" onclick="this.parentElement.parentElement.remove()">×</button>
                </div>
                <div class="modal-content">
                    <div class="detailed-fields">
                        ${Object.entries(this.currentEvaluation.fields).map(([fieldKey, fieldEval]) => `
                            <div class="detailed-field">
                                <div class="field-header">
                                    <span class="field-name">${this.getFieldLabel(fieldKey)}</span>
                                    <span class="field-confidence confidence-${fieldEval.level}">
                                        ${fieldEval.icon} ${fieldEval.confidence}%
                                    </span>
                                </div>
                                <div class="field-value">${fieldEval.value}</div>
                                <div class="field-details">
                                    <div class="detail-scores">
                                        <div class="score-item">
                                            <span class="score-label">格式</span>
                                            <span class="score-value">${fieldEval.details.formatScore}%</span>
                                        </div>
                                        <div class="score-item">
                                            <span class="score-label">长度</span>
                                            <span class="score-value">${fieldEval.details.lengthScore}%</span>
                                        </div>
                                        <div class="score-item">
                                            <span class="score-label">模式</span>
                                            <span class="score-value">${fieldEval.details.patternScore}%</span>
                                        </div>
                                        <div class="score-item">
                                            <span class="score-label">值</span>
                                            <span class="score-value">${fieldEval.details.valueScore}%</span>
                                        </div>
                                        <div class="score-item">
                                            <span class="score-label">上下文</span>
                                            <span class="score-value">${fieldEval.details.contextScore}%</span>
                                        </div>
                                    </div>
                                    ${fieldEval.issues.length > 0 ? `
                                        <div class="field-issues">
                                            <h5>问题:</h5>
                                            <ul>${fieldEval.issues.map(issue => `<li>${issue}</li>`).join('')}</ul>
                                        </div>
                                    ` : ''}
                                    ${fieldEval.recommendations.length > 0 ? `
                                        <div class="field-recommendations">
                                            <h5>建议:</h5>
                                            <ul>${fieldEval.recommendations.map(rec => `<li>${rec}</li>`).join('')}</ul>
                                        </div>
                                    ` : ''}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    /**
     * 继续使用数据（忽略置信度警告）
     */
    proceedWithConfidence() {
        if (this.currentEvaluation && this.currentEvaluation.overall.confidence < 50) {
            if (!confirm('当前数据置信度较低，确定要继续使用吗？建议先检查和修正数据。')) {
                return;
            }
        }

        this.showMessage('已确认使用AI解析数据', 'success');

        // 触发数据预览或直接填充
        if (this.dataPreviewManager) {
            this.showDataPreview();
        }
    }

    /**
     * 编辑解析数据
     */
    editParsedData() {
        // 这里可以打开数据编辑界面
        if (this.dataPreviewManager) {
            this.showDataPreview();
        } else {
            this.showMessage('数据预览功能未初始化', 'warning');
        }
    }

    /**
     * 导出数据
     */
    exportData() {
        if (!this.parsedData) {
            this.showMessage('没有可导出的数据', 'warning');
            return;
        }

        const dataText = JSON.stringify(this.parsedData, null, 2);
        const blob = new Blob([dataText], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `mdac-data-${Date.now()}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showMessage('数据已导出', 'success');
    }

    /**
     * 打开表单编辑器
     */
    openFormEditor() {
        chrome.tabs.create({ url: 'src/ui/form-editor/form-editor.html' });
    }

    /**
     * 打开AI设置
     */
    openAISettings() {
        chrome.runtime.openOptionsPage();
    }

    /**
     * 显示帮助
     */
    showHelp() {
        this.showModal('帮助信息', '这里是MDAC AI助手的使用说明...');
    }

    /**
     * 显示模态框
     */
    showModal(title, content) {
        const modal = document.getElementById('modal');
        const modalTitle = document.getElementById('modalTitle');
        const modalBody = document.getElementById('modalBody');

        if (modal && modalTitle && modalBody) {
            modalTitle.textContent = title;
            modalBody.innerHTML = content;
            modal.classList.add('show');
        }
    }

    /**
     * 关闭模态框
     */
    closeModal() {
        const modal = document.getElementById('modal');
        if (modal) {
            modal.classList.remove('show');
        }
    }

    /**
     * 处理模态框确认
     */
    handleModalConfirm() {
        // 处理确认操作
        this.closeModal();
    }

    // ===== 新增的分离解析功能 =====

    /**
     * 解析个人信息
     */
    async parsePersonalInfo() {
        console.log('🔍 parsePersonalInfo 方法被调用');
        console.log('🔍 this 对象:', this);
        console.log('🔍 this.callGeminiAPI:', typeof this.callGeminiAPI);
        
        const input = document.getElementById('personalInfoInput');
        if (!input || !input.value.trim()) {
            this.showMessage('请输入个人信息内容', 'warning');
            return;
        }

        try {
            this.showMessage('正在解析个人信息...', 'info');

            // 检查 callGeminiAPI 方法是否存在
            if (typeof this.callGeminiAPI !== 'function') {
                throw new Error('callGeminiAPI 方法不存在或不是函数');
            }

            // 使用专门的个人信息解析提示词
            const prompt = window.MDAC_AI_CONFIG?.AI_PROMPTS?.PERSONAL_INFO_PARSING ||
                          (typeof AI_PROMPTS !== 'undefined' ? AI_PROMPTS?.PERSONAL_INFO_PARSING : null);

            if (!prompt) {
                throw new Error('个人信息解析提示词未找到');
            }

            const result = await this.callGeminiAPI(
                prompt.replace('{content}', input.value.trim()),
                window.MDAC_AI_CONFIG?.AI_CONTEXTS?.PERSONAL_INFO_EXTRACTOR ||
                (typeof AI_CONTEXTS !== 'undefined' ? AI_CONTEXTS?.PERSONAL_INFO_EXTRACTOR : '')
            );

            if (result) {
                const personalData = this.parseAIResponse(result);
                this.fillPersonalFields(personalData);
                this.showMessage('个人信息解析完成！', 'success');

                // 清空输入框
                input.value = '';
            }
        } catch (error) {
            console.error('个人信息解析失败:', error);
            this.showMessage('个人信息解析失败: ' + error.message, 'error');
        }
    }

    /**
     * 解析旅行信息
     */
    async parseTravelInfo() {
        const input = document.getElementById('travelInfoInput');
        if (!input || !input.value.trim()) {
            this.showMessage('请输入旅行信息内容', 'warning');
            return;
        }

        try {
            this.showMessage('正在解析旅行信息...', 'info');

            const prompt = this.buildTravelInfoPrompt(input.value.trim());
            const result = await this.callGeminiAPI(prompt, 'Error parsing travel info');

            if (result) {
                const travelData = this.parseAIResponse(result);
                
                if (travelData && Object.keys(travelData).length > 0) {
                    // Google Maps地址标准化
                    if ((travelData.address || travelData.accommodationAddress) && this.googleMaps) {
                        try {
                            this.showMessage('正在使用Google Maps查询详细地址...', 'info');
                            
                            // 使用实际存在的地址字段
                            const addressToStandardize = travelData.address || travelData.accommodationAddress;
                            console.log('🏠 准备标准化地址:', addressToStandardize);
                            
                            const standardizedResult = await this.googleMaps.standardizeAddress(addressToStandardize);
                            console.log('📍 Google Maps标准化结果:', standardizedResult);

                            if (standardizedResult && standardizedResult.success && standardizedResult.mdacMapping) {
                                // 将Google Maps的MDAC映射合并到travel data中
                                const mdacData = standardizedResult.mdacMapping;
                                console.log('🗺️ MDAC映射数据:', mdacData);
                                
                                // 确保字段名正确映射
                                if (mdacData.address) travelData.address = mdacData.address;
                                if (mdacData.state) travelData.state = mdacData.state;
                                if (mdacData.city) travelData.city = mdacData.city;
                                if (mdacData.postcode) travelData.postcode = mdacData.postcode;
                                
                                // 保存标准化后的完整地址
                                travelData.standardizedAddress = standardizedResult.standardizedAddress;
                                
                                console.log('✅ 地址标准化完成，合并后的数据:', travelData);
                                this.showMessage('地址已通过Google Maps标准化', 'success');
                            } else {
                                console.warn('⚠️ Google Maps标准化失败或返回数据不完整:', standardizedResult);
                                this.showMessage('Google Maps未能解析地址，将使用原文填充', 'warning');
                            }
                        } catch (e) {
                            console.error("❌ Google Maps API调用异常:", e);
                            this.showMessage(`Google Maps API调用失败: ${e.message}`, 'error');
                        }
                    } else {
                        console.log('⏭️ 跳过Google Maps标准化：', {
                            hasAddress: !!(travelData.address || travelData.accommodationAddress),
                            hasGoogleMaps: !!this.googleMaps,
                            address: travelData.address || travelData.accommodationAddress
                        });
                    }

                    await this.fillTravelFields(travelData);
                this.showMessage('旅行信息解析完成！', 'success');
                    input.value = ''; // Clear input on success
                } else {
                    this.showMessage('未能从文本中解析出有效的出行信息', 'error');
                }
            }
        } catch (error) {
            console.error('旅行信息解析失败:', error);
            this.showMessage('旅行信息解析失败: ' + error.message, 'error');
        }
    }

    /**
     * 填充个人信息字段
     */
    fillPersonalFields(data) {
        console.log('👤 开始填充个人信息字段:', data);

        if (!data || Object.keys(data).length === 0) {
            console.warn('⚠️ 个人信息数据为空，跳过填充');
            this.showMessage('解析的个人信息数据为空', 'warning');
            return;
        }

        const personalFields = {
            'name': data.name,
            'passportNo': data.passportNo,
            'dateOfBirth': data.dateOfBirth,
            'nationality': data.nationality,
            'sex': data.sex,
            // 恢复护照到期日字段映射 - HTML中已添加此字段
            'passportExpiry': data.passportExpiry,
            'presetEmail': data.email || data.confirmEmail,
            'presetPhone': data.mobileNo ? (data.countryCode || '') + data.mobileNo : null
        };

        let filledCount = 0;
        let failedCount = 0;

        Object.entries(personalFields).forEach(([fieldId, value]) => {
            if (value !== null && value !== undefined && value !== '') {
                console.log(`🔍 尝试填充字段 ${fieldId}: ${value}`);

                const field = document.getElementById(fieldId);
                if (field) {
                    try {
                        let processedValue = value;

                        // 根据字段类型进行特殊处理
                        if (field.tagName === 'SELECT') {
                            // 下拉选择框 - 使用智能匹配
                            const option = this.smartMatchSelectOption(field, value, fieldId);
                            if (option) {
                                field.value = option.value;
                                console.log(`✅ 下拉框字段 ${fieldId} 填充成功: ${option.value} (${option.text})`);
                                
                                // 对于州属字段，触发级联更新
                                if (fieldId === 'accommodationState') {
                                    console.log('🔄 州属字段已更新，触发城市数据级联加载...');
                                    setTimeout(() => {
                                        if (typeof retrieveRefCity === 'function') {
                                            retrieveRefCity(option.value);
                                        }
                                    }, 100);
                                }
                            } else {
                                console.warn(`⚠️ 下拉框字段 ${fieldId} 未找到匹配选项: ${value}`);
                                failedCount++;
                                return;
                            }
                        } else if (field.type === 'date') {
                            // 日期字段 - 需要格式转换
                            processedValue = this.convertDateFormat(value);
                            field.value = processedValue;
                            console.log(`✅ 日期字段 ${fieldId} 填充成功: ${value} → ${processedValue}`);
                        } else {
                            // 普通输入框
                            field.value = processedValue;
                            console.log(`✅ 输入框字段 ${fieldId} 填充成功: ${processedValue}`);
                        }

                        field.classList.add('filled');

                        // 触发change事件
                        field.dispatchEvent(new Event('change', { bubbles: true }));
                        field.dispatchEvent(new Event('input', { bubbles: true }));

                        // 添加成功状态指示
                        const statusElement = field.parentElement.querySelector('.field-status');
                        if (statusElement) {
                            statusElement.className = 'field-status success';
                            statusElement.innerHTML = '<span class="status-icon">✅</span>';
                        }

                        filledCount++;
                    } catch (error) {
                        console.error(`❌ 填充字段 ${fieldId} 时出错:`, error);
                        failedCount++;
                    }
                } else {
                    console.warn(`⚠️ 未找到字段元素: ${fieldId}`);
                    failedCount++;
                }
            } else {
                console.log(`⏭️ 跳过空值字段 ${fieldId}`);
            }
        });

        // 显示填充结果
        if (filledCount > 0) {
            this.showMessage(`个人信息填充完成：${filledCount}个字段成功${failedCount > 0 ? `，${failedCount}个字段失败` : ''}`,
                           failedCount > 0 ? 'warning' : 'success');
            console.log(`📊 个人信息填充统计: 成功 ${filledCount}, 失败 ${failedCount}`);
        } else {
            this.showMessage('个人信息字段填充失败', 'error');
            console.error('❌ 所有个人信息字段填充都失败了');
        }
    }

    /**
     * 填充旅行信息字段
     */
    async fillTravelFields(data) {
        console.log('✈️ 开始填充旅行信息字段:', data);

        if (!data || Object.keys(data).length === 0) {
            console.warn('⚠️ 旅行信息数据为空，跳过填充');
            this.showMessage('解析的旅行信息数据为空', 'warning');
            return;
        }

        // 强制填充顺序以处理依赖关系 (州属 -> 城市)
        const fillOrder = [
            'dateOfDeparture', 'dateOfArrival', 'modeOfTravel', 'transportationNo', 'lastPortOfEmbarkation',
            'accommodationType', 'accommodationName',
            'accommodationState', 'accommodationCity', 'accommodationPostcode', 'accommodationAddress'
        ];

        const fieldMap = {
            // 统一字段映射 - 确保与AI配置文件中的字段名一致
            // 日期字段：AI返回arrivalDate/departureDate，直接映射到HTML字段
            arrivalDate: 'arrivalDate',              // AI字段 -> HTML字段ID (统一命名)
            departureDate: 'departureDate',          // AI字段 -> HTML字段ID (统一命名)
            // 兼容旧的字段名（向后兼容）
            dateOfArrival: 'arrivalDate',            // 兼容旧字段名
            dateOfDeparture: 'departureDate',        // 兼容旧字段名
            // 交通相关字段
            modeOfTravel: 'modeOfTravel',            // 交通方式字段已添加到HTML
            flightNo: 'flightNo',                    // AI返回flightNo，直接映射
            transportationNo: 'flightNo',            // 兼容旧字段名
            lastPortOfEmbarkation: 'lastPortOfEmbarkation',
            // 住宿相关字段映射 - 关键修复
            accommodation: 'accommodation',          // AI返回accommodation，直接映射
            accommodationType: 'accommodation',      // 兼容旧字段名
            accommodationName: 'accommodationName',  // 住宿名称（如果HTML中存在）
            // 地址字段：支持多种可能的字段名 - 关键修复
            address: 'address',                      // 主要地址字段
            accommodationAddress: 'address',         // 兼容字段名
            // 地理位置字段 - 关键修复
            state: 'state',                          // 州属字段
            accommodationState: 'state',             // 兼容旧字段名
            city: 'city',                            // 城市字段
            accommodationCity: 'city',               // 兼容旧字段名
            postcode: 'postcode',                    // 邮政编码字段
            accommodationPostcode: 'postcode'        // 兼容旧字段名
        };

        // 简化字段映射逻辑 - 大部分字段现在直接通过fieldMap处理
        // 只保留特殊情况的映射
        const mdacFieldMap = {
            // 特殊映射：fillOrder中的键名 -> HTML字段ID
            'accommodationType': 'accommodation',         // fillOrder键 -> HTML字段ID
            'accommodationState': 'state',                // fillOrder键 -> HTML字段ID
            'accommodationCity': 'city',                  // fillOrder键 -> HTML字段ID
            'accommodationPostcode': 'postcode',          // fillOrder键 -> HTML字段ID
            'accommodationAddress': 'address',            // fillOrder键 -> HTML字段ID
            'dateOfDeparture': 'departureDate',           // fillOrder键 -> HTML字段ID
            'dateOfArrival': 'arrivalDate',               // fillOrder键 -> HTML字段ID
            'transportationNo': 'flightNo'                // fillOrder键 -> HTML字段ID
        };

        // 遍历所有可能的数据字段，不仅限于fillOrder
        const allPossibleFields = [...fillOrder];

        // 添加直接从data中获取的字段
        Object.keys(data).forEach(dataKey => {
            if (!allPossibleFields.includes(dataKey)) {
                allPossibleFields.push(dataKey);
            }
        });

        console.log('🔍 开始填充旅行字段，可用数据:', data);
        console.log('📋 将处理的字段列表:', allPossibleFields);

        for (const key of allPossibleFields) {
            // 优先使用直接的数据键，然后尝试映射
            let value = data[key];
            if (value === undefined || value === null || value === '') {
                // 尝试通过fieldMap查找
                const dataKey = fieldMap[key];
                if (dataKey) {
                    value = data[dataKey];
                }
            }

            if (value !== undefined && value !== null && value !== '') {
                // 确定HTML元素ID
                const elementId = fieldMap[key] || mdacFieldMap[key] || key;
                const element = document.getElementById(elementId);

                // 特殊调试：地址字段
                if (key === 'address' || key === 'accommodationAddress' || elementId === 'address') {
                    console.log(`🏠 地址字段调试信息:`, {
                        key: key,
                        value: value,
                        elementId: elementId,
                        elementExists: !!element,
                        elementType: element ? element.tagName : 'N/A'
                    });
                }

                if (!element) {
                    console.warn(`⚠️ 未找到字段元素: ${elementId} (数据键: ${key})`);
                    continue;
                }

                console.log(`🔄 处理字段 ${key} -> ${elementId}, 原始值: ${value}`);

                try {
                    let processedValue = value;

                    // 日期字段特殊处理：转换格式
                    if (elementId === 'arrivalDate' || elementId === 'departureDate') {
                        processedValue = this.convertDateFormat(value);
                        console.log(`📅 日期格式转换: ${value} -> ${processedValue}`);
                    }

                    // 根据元素类型进行填充
                    if (key === 'accommodationState' || elementId === 'state') {
                        await this.setSelectValueAndWaitForUpdate(element, processedValue, elementId);
                    } else if (element.tagName === 'SELECT') {
                        const selectedOption = this.smartMatchSelectOption(element, processedValue, elementId);
                        if (selectedOption) {
                            element.value = selectedOption.value;
                            element.dispatchEvent(new Event('change', { bubbles: true }));
                            console.log(`✅ 下拉框设置成功 ${elementId} = ${selectedOption.value}`);
                        } else {
                            console.warn(`❌ 无法为下拉框 ${elementId} 找到匹配选项: ${processedValue}`);
                            this.updateFieldStatus(elementId, 'error', { message: `无法匹配选项: ${processedValue}` });
                            continue;
                        }
                    } else {
                        // 普通输入框
                        element.value = processedValue;
                        element.dispatchEvent(new Event('input', { bubbles: true }));
                        element.dispatchEvent(new Event('change', { bubbles: true }));
                        console.log(`✅ 输入框设置成功 ${elementId} = ${processedValue}`);
                    }

                    element.classList.add('filled');
                    this.updateFieldStatus(elementId, 'success');
                } catch (error) {
                    console.error(`❌ 填充字段 ${elementId} 时出错:`, error);
                    this.updateFieldStatus(elementId, 'error', { message: error.message });
                }
            } else {
                console.log(`⏭️ 跳过空值字段: ${key}`);
            }
        }
    }

    async setSelectValueAndWaitForUpdate(selectElement, value, fieldId) {
        const initialValue = selectElement.value;
    
        const selectedOption = this.smartMatchSelectOption(selectElement, value, fieldId);
        if (selectedOption) {
            selectElement.value = selectedOption.value;
            selectElement.dispatchEvent(new Event('change', { bubbles: true }));
            console.log(`✅ 成功设置州属 ${fieldId} = ${selectedOption.value}`);
        } else {
            console.warn(`❌ 无法为州属字段 ${fieldId} 找到匹配选项: ${value}`);
            throw new Error(`无法匹配州属选项: ${value}`);
        }
    
        if (selectElement.value !== initialValue && selectElement.value !== '') {
            const cityDropdown = document.getElementById('accommodationCity') || document.getElementById('city');
            if (cityDropdown) {
                console.log('State changed, waiting for city options to update...');
                this.showMessage('正在加载城市列表...', 'info');
                try {
                    await this.waitForCityOptions(cityDropdown);
                    this.showMessage('城市列表已加载', 'success');
                } catch (e) {
                    console.error(e);
                    this.showMessage(e.message, 'error');
                }
            }
        }
    }
    
    async waitForCityOptions(cityDropdown, timeout = 5000) {
        const startTime = Date.now();
        const initialOptionCount = cityDropdown.options.length;
    
        return new Promise((resolve, reject) => {
            const interval = setInterval(() => {
                const newOptionCount = cityDropdown.options.length;
    
                if (newOptionCount > 1 && newOptionCount !== initialOptionCount) {
                    clearInterval(interval);
                    setTimeout(() => resolve(), 150);
                } else if (Date.now() - startTime > timeout) {
                    clearInterval(interval);
                    reject(new Error('等待城市选项更新超时'));
        }
            }, 100);
        });
    }

    /**
     * 清除所有数据
     */
    clearAllData() {
        // 清除输入框
        const inputs = ['personalInfoInput', 'travelInfoInput', 'presetEmail', 'presetPhone'];
        inputs.forEach(id => {
            const element = document.getElementById(id);
            if (element) element.value = '';
        });

        // 清除所有字段
        const allFields = document.querySelectorAll('.field-input, .preset-input');
        allFields.forEach(field => {
            field.value = '';
            field.classList.remove('filled');
        });

        // 清除状态指示器
        const statusElements = document.querySelectorAll('.field-status');
        statusElements.forEach(status => {
            status.className = 'field-status';
        });

        this.showMessage('所有数据已清除', 'info');
    }

    /**
     * 预览所有数据
     */
    previewAllData() {
        const allData = this.collectAllFormData();
        const preview = this.formatDataPreview(allData);
        this.showModal('数据预览', preview);
    }

    /**
     * 收集所有表单数据
     */
    collectAllFormData() {
        const data = {};

        // 收集所有字段数据
        const fields = document.querySelectorAll('.field-input, .preset-input');
        fields.forEach(field => {
            if (field.value) {
                data[field.id] = field.value;
            }
        });

        return data;
    }

    /**
     * 格式化数据预览
     */
    formatDataPreview(data) {
        let html = '<div class="data-preview">';

        if (Object.keys(data).length === 0) {
            html += '<p>暂无数据</p>';
        } else {
            html += '<table class="preview-table">';
            Object.entries(data).forEach(([key, value]) => {
                html += `<tr><td><strong>${key}:</strong></td><td>${value}</td></tr>`;
            });
            html += '</table>';
        }

        html += '</div>';
        return html;
    }

    /**
     * 更新到MDAC页面
     */
    async updateToMDAC() {
        if (!this.isMDACPage) {
            this.showMessage('请先打开MDAC网站', 'warning');
            return;
        }

        try {
            const data = this.collectAllFormData();
            if (Object.keys(data).length === 0) {
                this.showMessage('没有数据可以更新', 'warning');
                return;
            }

            this.showMessage('正在更新到MDAC页面...', 'info');

            // 这里调用现有的填充方法
            await this.fillForm();

        } catch (error) {
            console.error('更新到MDAC失败:', error);
            this.showMessage('更新失败: ' + error.message, 'error');
        }
    }

    /**
     * 编辑预设信息
     */
    editPresetInfo() {
        const emailField = document.getElementById('presetEmail');
        const phoneField = document.getElementById('presetPhone');

        if (emailField) emailField.focus();

        this.showMessage('请编辑常用邮箱和电话信息', 'info');
    }

    /**
     * 保存数据
     */
    async saveData() {
        try {
            const data = this.collectAllFormData();

            // 保存到Chrome存储
            await chrome.storage.local.set({
                'mdac_saved_data': data,
                'mdac_save_timestamp': Date.now()
            });

            this.showMessage('数据已保存', 'success');
        } catch (error) {
            console.error('保存数据失败:', error);
            this.showMessage('保存失败: ' + error.message, 'error');
        }
    }

    /**
     * 显示设置
     */
    showSettings() {
        this.showModal('设置', '<p>设置功能开发中...</p>');
    }

    // ===== 自动解析功能 =====

    /**
     * 设置自动解析事件监听器
     */
    setupAutoParseListeners() {
        // 个人信息输入框的自动解析
        const personalInput = document.getElementById('personalInfoInput');
        const personalToggle = document.getElementById('autoParsePersonalEnabled');
        const personalCancel = document.getElementById('cancelAutoParsePersonal');

        if (personalInput) {
            personalInput.addEventListener('input', () => {
                this.handleAutoParseInput('personal', personalInput.value.trim());
            });
        }

        if (personalToggle) {
            personalToggle.addEventListener('change', (e) => {
                this.autoParseSettings.personal = e.target.checked;
                this.saveAutoParseSettings();
            });
        }

        if (personalCancel) {
            personalCancel.addEventListener('click', () => {
                this.cancelAutoParse('personal');
            });
        }

        // 旅行信息输入框的自动解析
        const travelInput = document.getElementById('travelInfoInput');
        const travelToggle = document.getElementById('autoParseTravel Enabled');
        const travelCancel = document.getElementById('cancelAutoParseTravel');

        if (travelInput) {
            travelInput.addEventListener('input', () => {
                this.handleAutoParseInput('travel', travelInput.value.trim());
            });
        }

        if (travelToggle) {
            travelToggle.addEventListener('change', (e) => {
                this.autoParseSettings.travel = e.target.checked;
                this.saveAutoParseSettings();
            });
        }

        if (travelCancel) {
            travelCancel.addEventListener('click', () => {
                this.cancelAutoParse('travel');
            });
        }

        // 加载保存的自动解析设置
        this.loadAutoParseSettings();
    }

    /**
     * 处理自动解析输入
     * @param {string} type - 解析类型 ('personal' 或 'travel')
     * @param {string} content - 输入内容
     */
    handleAutoParseInput(type, content) {
        // 清除之前的定时器
        if (this.autoParseTimeouts[type]) {
            clearTimeout(this.autoParseTimeouts[type]);
            this.autoParseTimeouts[type] = null;
        }

        // 隐藏状态指示器
        this.hideAutoParseStatus(type);

        // 如果内容为空或自动解析被禁用，直接返回
        if (!content || !this.autoParseSettings[type]) {
            return;
        }

        // 显示倒计时状态
        this.showAutoParseCountdown(type);

        // 设置新的定时器
        this.autoParseTimeouts[type] = setTimeout(() => {
            this.triggerAutoParse(type);
        }, this.autoParseSettings.delay);
    }

    /**
     * 显示自动解析倒计时
     * @param {string} type - 解析类型
     */
    showAutoParseCountdown(type) {
        const statusElement = document.getElementById(`autoParse${type.charAt(0).toUpperCase() + type.slice(1)}Status`);
        const countdownElement = document.getElementById(`${type}CountdownText`);

        if (!statusElement || !countdownElement) return;

        statusElement.style.display = 'block';
        statusElement.classList.add('auto-parsing');

        let countdown = Math.ceil(this.autoParseSettings.delay / 1000);
        countdownElement.textContent = `${countdown}秒后自动解析...`;

        // 更新倒计时显示
        const countdownInterval = setInterval(() => {
            countdown--;
            if (countdown > 0) {
                countdownElement.textContent = `${countdown}秒后自动解析...`;
            } else {
                clearInterval(countdownInterval);
                countdownElement.textContent = '正在解析...';
            }
        }, 1000);

        // 保存interval引用以便取消时清除
        statusElement.dataset.countdownInterval = countdownInterval;
    }

    /**
     * 隐藏自动解析状态
     * @param {string} type - 解析类型
     */
    hideAutoParseStatus(type) {
        const statusElement = document.getElementById(`autoParse${type.charAt(0).toUpperCase() + type.slice(1)}Status`);

        if (!statusElement) return;

        statusElement.style.display = 'none';
        statusElement.classList.remove('auto-parsing');

        // 清除倒计时interval
        const countdownInterval = statusElement.dataset.countdownInterval;
        if (countdownInterval) {
            clearInterval(parseInt(countdownInterval));
            delete statusElement.dataset.countdownInterval;
        }
    }

    /**
     * 取消自动解析
     * @param {string} type - 解析类型
     */
    cancelAutoParse(type) {
        // 清除定时器
        if (this.autoParseTimeouts[type]) {
            clearTimeout(this.autoParseTimeouts[type]);
            this.autoParseTimeouts[type] = null;
        }

        // 隐藏状态指示器
        this.hideAutoParseStatus(type);

        // 显示取消消息
        this.showMessage('已取消自动解析', 'info');
    }

    /**
     * 触发自动解析
     * @param {string} type - 解析类型
     */
    async triggerAutoParse(type) {
        try {
            // 隐藏倒计时状态
            this.hideAutoParseStatus(type);

            // 显示解析中状态
            this.showMessage(`正在自动解析${type === 'personal' ? '个人' : '旅行'}信息...`, 'info');

            // 根据类型调用相应的解析方法
            if (type === 'personal') {
                await this.parsePersonalInfo();
            } else if (type === 'travel') {
                await this.parseTravelInfo();
            }

            // 解析完成后的处理
            this.showMessage(`${type === 'personal' ? '个人' : '旅行'}信息自动解析完成`, 'success');

            // 自动显示数据预览 - 修复this上下文问题
            if (this.dataPreviewManager && typeof this.showDataPreview === 'function') {
                setTimeout(() => {
                    try {
                        this.showDataPreview();
                    } catch (error) {
                        console.error('显示数据预览失败:', error);
                        // 备用方案：显示简单预览
                        this.showSimpleDataPreview();
                    }
                }, 1000);
            } else {
                // 如果dataPreviewManager不可用，使用备用方案
                console.log('📋 数据预览管理器不可用，使用简单预览');
                setTimeout(() => {
                    try {
                        this.showSimpleDataPreview();
                    } catch (error) {
                        console.error('显示简单数据预览失败:', error);
                        this.showMessage('数据预览功能暂时不可用', 'warning');
                    }
                }, 1000);
            }

        } catch (error) {
            console.error('自动解析失败:', error);
            this.showMessage(`自动解析失败: ${error.message}`, 'error');
        }
    }

    /**
     * 显示简单数据预览（备用方案）
     * 当dataPreviewManager不可用时使用此方法
     */
    showSimpleDataPreview() {
        console.log('📋 显示简单数据预览');

        // 收集所有表单数据
        const allData = this.collectAllFormData();
        if (!allData || Object.keys(allData).length === 0) {
            this.showMessage('没有可预览的数据', 'info');
            return;
        }

        // 构建预览HTML
        let previewHtml = '<div class="data-preview-container"><h3>📋 数据预览</h3>';

        // 按类别组织数据
        const categories = {
            personal: { title: '👤 个人信息', fields: [] },
            travel: { title: '✈️ 旅行信息', fields: [] },
            accommodation: { title: '🏨 住宿信息', fields: [] }
        };

        // 字段分类映射
        const fieldCategories = {
            name: 'personal', passportNo: 'personal', dateOfBirth: 'personal',
            nationality: 'personal', sex: 'personal', presetEmail: 'personal', presetPhone: 'personal',
            arrivalDate: 'travel', departureDate: 'travel', flightNo: 'travel',
            accommodation: 'accommodation', address: 'accommodation',
            state: 'accommodation', city: 'accommodation', postcode: 'accommodation'
        };

        // 分类数据
        for (const [key, value] of Object.entries(allData)) {
            if (value && value.toString().trim() !== '') {
                const category = fieldCategories[key] || 'travel';
                const label = this.getFieldLabel(key);
                categories[category].fields.push({ label, value });
            }
        }

        // 生成分类预览HTML
        for (const [catKey, catData] of Object.entries(categories)) {
            if (catData.fields.length > 0) {
                previewHtml += `<div class="preview-category">
                    <h4>${catData.title}</h4>`;

                catData.fields.forEach(field => {
                    previewHtml += `<div class="preview-item">
                        <strong>${field.label}:</strong>
                        <span class="preview-value">${field.value}</span>
                    </div>`;
                });

                previewHtml += '</div>';
            }
        }

        previewHtml += `
            <div class="preview-actions">
                <button onclick="document.getElementById('modal').style.display='none'" class="btn secondary">关闭</button>
            </div>
        </div>`;

        // 显示模态框
        this.showModal('数据预览', previewHtml);

        console.log('✅ 简单数据预览已显示');
    }

    /**
     * 保存自动解析设置
     */
    async saveAutoParseSettings() {
        try {
            await chrome.storage.local.set({
                'mdac_auto_parse_settings': this.autoParseSettings
            });
        } catch (error) {
            console.error('保存自动解析设置失败:', error);
        }
    }

    /**
     * 加载自动解析设置
     */
    async loadAutoParseSettings() {
        try {
            const result = await chrome.storage.local.get(['mdac_auto_parse_settings']);
            if (result.mdac_auto_parse_settings) {
                this.autoParseSettings = { ...this.autoParseSettings, ...result.mdac_auto_parse_settings };

                // 更新UI状态
                const personalToggle = document.getElementById('autoParsePersonalEnabled');
                const travelToggle = document.getElementById('autoParseTravel Enabled');

                if (personalToggle) {
                    personalToggle.checked = this.autoParseSettings.personal;
                }
                if (travelToggle) {
                    travelToggle.checked = this.autoParseSettings.travel;
                }
            }
        } catch (error) {
            console.error('加载自动解析设置失败:', error);
        }
    }

    /**
     * 智能匹配下拉框选项
     * 特别针对州属和城市代码进行智能匹配
     */
    smartMatchSelectOption(field, value, fieldId) {
        console.log(`🔍 智能匹配下拉框 ${fieldId}: ${value}`);
        
        // 城市代码智能映射表
        const cityMapping = {
            // 直接代码映射
            '0100': '0100',  // Johor
            '0118': '0118',  // Johor Bahru
            
            // 地名到代码的映射
            'johor bahru': '0118',
            '新山': '0118',
            '新山乐高': '0118',
            'legoland': '0118',
            '乐高': '0118',
            'johor': '0100',
            '柔佛': '0100',
            
            // 其他常见城市
            'kuala lumpur': '1400',
            '吉隆坡': '1400',
            'kl': '1400',
            'george town': '1000',
            'penang': '1000',
            '槟城': '1000',
            'melaka': '0700',
            '马六甲': '0700'
        };
        
        // 州属代码映射表
        const stateMapping = {
            '01': '01', // Johor
            '14': '14', // KL
            '07': '07', // Penang
            '04': '04', // Melaka
            'johor': '01',
            '柔佛': '01',
            'kuala lumpur': '14',
            '吉隆坡': '14',
            'kl': '14'
        };
        
        // 性别字段映射表
        const sexMapping = {
            '1': '1',     // 男性
            '2': '2',     // 女性
            'male': '1',
            'female': '2',
            '男': '1',
            '女': '2',
            '男性': '1',
            '女性': '2'
        };
        
        // 1. 尝试直接匹配值
        let option = Array.from(field.options).find(opt => opt.value === value);
        if (option) {
            console.log(`✅ 直接匹配成功: ${value}`);
            return option;
        }
        
        // 2. 针对城市字段的特殊处理
        if (fieldId === 'accommodationCity' || fieldId === 'city') {
            // 首先尝试智能映射
            const lowerValue = value.toString().toLowerCase();
            const mappedCode = cityMapping[lowerValue];

            if (mappedCode) {
                option = Array.from(field.options).find(opt => opt.value === mappedCode);
                if (option) {
                    console.log(`✅ 城市智能映射成功: ${value} → ${mappedCode}`);
                    return option;
                } else {
                    console.warn(`⚠️ 映射的城市代码 ${mappedCode} 在下拉选项中不存在`);
                    // 如果映射的代码不存在，尝试动态添加选项
                    if (this.mdacValidator) {
                        this.addMissingCityOption(field, mappedCode, value);
                        // 重新查找选项
                        option = Array.from(field.options).find(opt => opt.value === mappedCode);
                        if (option) {
                            console.log(`✅ 动态添加城市选项成功: ${value} → ${mappedCode}`);
                            return option;
                        }
                    }
                }
            }

            // 关键词匹配
            if (lowerValue.includes('johor') || lowerValue.includes('新山') ||
                lowerValue.includes('legoland') || lowerValue.includes('乐高')) {
                option = Array.from(field.options).find(opt => opt.value === '0118');
                if (option) {
                    console.log(`✅ 关键词匹配到新山: ${value} → 0118`);
                    return option;
                } else {
                    // 尝试动态添加 0118 选项
                    console.warn(`⚠️ 城市代码 0118 在下拉选项中不存在，尝试动态添加`);
                    if (this.mdacValidator) {
                        this.addMissingCityOption(field, '0118', value);
                        option = Array.from(field.options).find(opt => opt.value === '0118');
                        if (option) {
                            console.log(`✅ 动态添加城市选项 0118 成功`);
                            return option;
                        }
                    }
                }
            }
        }
        
        // 3. 针对州属字段的特殊处理
        if (fieldId === 'accommodationState' || fieldId === 'state') {
            const lowerValue = value.toString().toLowerCase();
            const mappedCode = stateMapping[lowerValue];
            
            if (mappedCode) {
                option = Array.from(field.options).find(opt => opt.value === mappedCode);
                if (option) {
                    console.log(`✅ 州属智能映射成功: ${value} → ${mappedCode}`);
                    return option;
                }
            }
        }
        
        // 4. 针对性别字段的特殊处理
        if (fieldId === 'sex') {
            const lowerValue = value.toString().toLowerCase();
            const mappedCode = sexMapping[value] || sexMapping[lowerValue];
            
            if (mappedCode) {
                option = Array.from(field.options).find(opt => opt.value === mappedCode);
                if (option) {
                    console.log(`✅ 性别智能映射成功: ${value} → ${mappedCode}`);
                    return option;
                } else {
                    console.warn(`⚠️ 映射的性别代码 ${mappedCode} 在下拉选项中不存在`);
                    console.log('性别字段可用选项:', Array.from(field.options).map(opt => `${opt.value}: ${opt.text}`));
                }
            }
        }
        
        // 5. 尝试文本包含匹配（忽略大小写）
        const lowerValue = value.toString().toLowerCase();
        option = Array.from(field.options).find(opt => 
            opt.text.toLowerCase().includes(lowerValue) || 
            lowerValue.includes(opt.text.toLowerCase())
        );
        if (option) {
            console.log(`✅ 文本包含匹配成功: ${value} → ${option.value}`);
            return option;
        }
        
        // 6. 尝试部分匹配
        option = Array.from(field.options).find(opt => {
            const optText = opt.text.toLowerCase().replace(/\s+/g, '');
            const searchValue = lowerValue.replace(/\s+/g, '');
            return optText.includes(searchValue) || searchValue.includes(optText);
        });
        
        if (option) {
            console.log(`✅ 部分匹配成功: ${value} → ${option.value}`);
            return option;
        }
        
        console.warn(`❌ 未找到匹配选项: ${fieldId} = ${value}`);
        console.log('可用选项:', Array.from(field.options).map(opt => `${opt.value}: ${opt.text}`));
        return null;
    }

    /**
     * 动态添加缺失的城市选项
     */
    async addMissingCityOption(selectField, cityCode, originalValue) {
        if (!this.mdacValidator) return;

        try {
            await this.mdacValidator.ensureDataLoaded();
            const cityDetails = await this.mdacValidator.getCityDetails(cityCode);

            if (cityDetails) {
                const option = document.createElement('option');
                option.value = cityCode;
                option.textContent = `${cityDetails.name} (${cityDetails.chinese})`;
                selectField.appendChild(option);
                console.log(`✅ 动态添加城市选项: ${cityCode} - ${cityDetails.name}`);
            } else {
                console.warn(`⚠️ 无法获取城市 ${cityCode} 的详细信息`);
            }
        } catch (error) {
            console.error(`❌ 动态添加城市选项失败: ${error.message}`);
        }
    }

    /**
     * 构建旅行信息AI解析提示词
     * @param {string} content - 用户输入的旅行信息文本
     * @returns {string} - 用于AI解析的完整prompt
     */
    buildTravelInfoPrompt(content) {
        // 优先使用全局配置
        const prompt = window.MDAC_AI_CONFIG?.AI_PROMPTS?.TRAVEL_INFO_PARSING ||
                       (typeof AI_PROMPTS !== 'undefined' ? AI_PROMPTS?.TRAVEL_INFO_PARSING : null);
        if (!prompt) throw new Error('旅行信息解析提示词未找到');
        return prompt.replace('{content}', content);
    }
}

// ============================================================================
// 城市查看器功能
// ============================================================================

/**
 * 城市查看器相关方法的扩展
 */
Object.assign(MDACAssistantSidePanel.prototype, {
    /**
     * 初始化城市查看器
     */
    async initializeCityViewer() {
        console.log('🏙️ 初始化城市查看器...');
        
        // 初始化MDACValidator以加载城市数据
        if (!this.mdacValidator) {
            this.mdacValidator = new MDACValidator();
            // 等待数据加载完成
            await new Promise(resolve => {
                const checkDataLoaded = () => {
                    if (this.mdacValidator.codeMappings && this.mdacValidator.codeMappings.cities) {
                        resolve();
                    } else {
                        setTimeout(checkDataLoaded, 100);
                    }
                };
                checkDataLoaded();
            });
        }
        
        // 初始化州属下拉列表
        this.initializeStateFilter();
        
        // 设置默认视图模式
        this.currentViewMode = 'list';
        this.currentCities = [];
        this.filteredCities = [];
        
        console.log('✅ 城市查看器初始化完成');
    },

    /**
     * 切换城市查看器显示/隐藏
     */
    async toggleCityViewer() {
        const cityViewerArea = document.getElementById('cityViewerArea');
        if (!cityViewerArea) return;
        
        if (cityViewerArea.style.display === 'none') {
            // 显示城市查看器
            await this.showCityViewer();
        } else {
            // 隐藏城市查看器
            this.closeCityViewer();
        }
    },

    /**
     * 显示城市查看器
     */
    async showCityViewer() {
        console.log('🏙️ 显示城市查看器');
        
        // 初始化城市查看器（如果还未初始化）
        if (!this.mdacValidator) {
            await this.initializeCityViewer();
        }
        
        const cityViewerArea = document.getElementById('cityViewerArea');
        if (cityViewerArea) {
            cityViewerArea.style.display = 'block';
            
            // 加载并显示所有城市
            this.loadAllCities();
            
            // 聚焦到搜索框
            const searchInput = document.getElementById('citySearchInput');
            if (searchInput) {
                setTimeout(() => searchInput.focus(), 100);
            }
        }
    },

    /**
     * 关闭城市查看器
     */
    closeCityViewer() {
        console.log('🏙️ 关闭城市查看器');
        
        const cityViewerArea = document.getElementById('cityViewerArea');
        if (cityViewerArea) {
            cityViewerArea.style.display = 'none';
        }
        
        // 清除搜索内容
        const searchInput = document.getElementById('citySearchInput');
        if (searchInput) {
            searchInput.value = '';
        }
        
        // 重置过滤器
        const stateFilter = document.getElementById('stateFilter');
        if (stateFilter) {
            stateFilter.value = '';
        }
    },

    /**
     * 初始化州属过滤器
     */
    async initializeStateFilter() {
        const stateFilter = document.getElementById('stateFilter');
        if (!stateFilter) return;

        // 确保验证器数据已加载
        if (!this.mdacValidator) {
            console.warn('⚠️ 验证器未初始化，无法加载州属过滤器');
            return;
        }

        try {
            await this.mdacValidator.ensureDataLoaded();
            if (!this.mdacValidator.codeMappings || !this.mdacValidator.codeMappings.states) {
                console.warn('⚠️ 州属数据未加载');
                return;
            }
        } catch (error) {
            console.error('❌ 加载州属数据失败:', error);
            return;
        }
        
        // 清空现有选项
        stateFilter.innerHTML = '<option value="">所有州属</option>';
        
        // 添加州属选项
        Object.entries(this.mdacValidator.codeMappings.states).forEach(([code, name]) => {
            const option = document.createElement('option');
            option.value = code;
            option.textContent = name;
            stateFilter.appendChild(option);
        });
        
        console.log('✅ 州属过滤器初始化完成');
    },

    /**
     * 加载所有城市
     */
    loadAllCities() {
        console.log('🏙️ 加载所有城市数据...');
        
        if (!this.mdacValidator) {
            console.error('❌ MDACValidator未初始化');
            return;
        }
        
        this.currentCities = this.mdacValidator.getAllCitiesWithDetails();
        this.filteredCities = [...this.currentCities];
        
        this.renderCityList();
        this.updateCityStats();
        
        console.log(`✅ 加载完成，共 ${this.currentCities.length} 个城市`);
    },

    /**
     * 处理城市搜索输入
     */
    handleCitySearch(event) {
        const query = event.target.value.trim();
        
        // 防抖处理
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }
        
        this.searchTimeout = setTimeout(() => {
            this.performCitySearchWithQuery(query);
        }, 300);
    },

    /**
     * 执行城市搜索
     */
    performCitySearch() {
        const searchInput = document.getElementById('citySearchInput');
        if (searchInput) {
            this.performCitySearchWithQuery(searchInput.value.trim());
        }
    },

    /**
     * 使用指定查询执行城市搜索
     */
    performCitySearchWithQuery(query) {
        console.log('🔍 搜索城市:', query);
        
        if (!query) {
            // 如果搜索为空，显示当前过滤的城市
            const stateFilter = document.getElementById('stateFilter');
            if (stateFilter && stateFilter.value) {
                this.handleStateFilter({ target: stateFilter });
            } else {
                this.filteredCities = [...this.currentCities];
            }
        } else {
            // 执行搜索
            if (this.mdacValidator) {
                const searchResults = this.mdacValidator.searchCities(query, 50);
                this.filteredCities = searchResults;
            } else {
                // 备用搜索方法
                const lowerQuery = query.toLowerCase();
                this.filteredCities = this.currentCities.filter(city =>
                    city.searchText.includes(lowerQuery)
                );
            }
        }
        
        this.renderCityList();
        this.updateCityStats();
    },

    /**
     * 处理州属过滤
     */
    handleStateFilter(event) {
        const stateCode = event.target.value;
        console.log('🏛️ 过滤州属:', stateCode);
        
        if (!stateCode) {
            // 显示所有城市
            this.filteredCities = [...this.currentCities];
        } else {
            // 过滤指定州属的城市
            if (this.mdacValidator) {
                this.filteredCities = this.mdacValidator.getCitiesByStateWithDetails(stateCode);
            } else {
                this.filteredCities = this.currentCities.filter(city => city.stateCode === stateCode);
            }
        }
        
        // 清除搜索框
        const searchInput = document.getElementById('citySearchInput');
        if (searchInput) {
            searchInput.value = '';
        }
        
        this.renderCityList();
        this.updateCityStats();
    },

    /**
     * 显示热门目的地
     */
    showPopularDestinations() {
        console.log('🌟 显示热门目的地');
        
        if (this.mdacValidator) {
            this.filteredCities = this.mdacValidator.getPopularDestinationsWithDetails();
        } else {
            // 备用方案：显示几个知名城市
            this.filteredCities = this.currentCities.filter(city =>
                ['KUL', 'JHR', 'PEN', 'KCH', 'KBK', 'IPH'].includes(city.code)
            );
        }
        
        // 清除过滤器
        const stateFilter = document.getElementById('stateFilter');
        if (stateFilter) {
            stateFilter.value = '';
        }
        
        // 清除搜索框
        const searchInput = document.getElementById('citySearchInput');
        if (searchInput) {
            searchInput.value = '';
        }
        
        this.renderCityList();
        this.updateCityStats();
    },

    /**
     * 切换到列表视图
     */
    switchToListView() {
        this.currentViewMode = 'list';
        this.updateViewButtons();
        this.renderCityList();
    },

    /**
     * 切换到网格视图
     */
    switchToGridView() {
        this.currentViewMode = 'grid';
        this.updateViewButtons();
        this.renderCityList();
    },

    /**
     * 更新视图按钮状态
     */
    updateViewButtons() {
        const listBtn = document.getElementById('listViewBtn');
        const gridBtn = document.getElementById('gridViewBtn');
        
        if (listBtn && gridBtn) {
            listBtn.classList.toggle('active', this.currentViewMode === 'list');
            gridBtn.classList.toggle('active', this.currentViewMode === 'grid');
        }
    },

    /**
     * 渲染城市列表
     */
    renderCityList() {
        const cityList = document.getElementById('cityList');
        if (!cityList) return;
        
        if (!this.filteredCities || this.filteredCities.length === 0) {
            cityList.innerHTML = '<div class="no-cities">未找到匹配的城市</div>';
            return;
        }
        
        // 根据视图模式渲染
        if (this.currentViewMode === 'grid') {
            this.renderGridView(cityList);
        } else {
            this.renderListView(cityList);
        }
    },

    /**
     * 渲染列表视图
     */
    renderListView(container) {
        container.className = 'city-list list-view';
        
        const html = this.filteredCities.map(city => `
            <div class="city-item" data-code="${city.code}">
                <div class="city-main-info">
                    <div class="city-name">
                        <span class="name-en">${city.name}</span>
                        <span class="name-zh">${city.chinese}</span>
                    </div>
                    <div class="city-details">
                        <span class="city-state">📍 ${city.stateName}</span>
                        <span class="city-postcode">📮 ${city.postcode}</span>
                    </div>
                </div>
                <div class="city-actions">
                    <button class="use-city-btn" onclick="mdacAssistant.useCityForForm('${city.code}')">
                        使用
                    </button>
                </div>
            </div>
        `).join('');
        
        container.innerHTML = html;
    },

    /**
     * 渲染网格视图
     */
    renderGridView(container) {
        container.className = 'city-list grid-view';
        
        const html = this.filteredCities.map(city => `
            <div class="city-card" data-code="${city.code}">
                <div class="city-card-header">
                    <div class="city-name">
                        <div class="name-en">${city.name}</div>
                        <div class="name-zh">${city.chinese}</div>
                    </div>
                </div>
                <div class="city-card-body">
                    <div class="city-info-item">
                        <span class="info-label">州属:</span>
                        <span class="info-value">${city.stateName}</span>
                    </div>
                    <div class="city-info-item">
                        <span class="info-label">邮编:</span>
                        <span class="info-value">${city.postcode}</span>
                    </div>
                </div>
                <div class="city-card-footer">
                    <button class="use-city-btn" onclick="mdacAssistant.useCityForForm('${city.code}')">
                        使用此城市
                    </button>
                </div>
            </div>
        `).join('');
        
        container.innerHTML = html;
    },

    /**
     * 更新城市统计信息
     */
    updateCityStats() {
        const statsEl = document.getElementById('cityListStats');
        if (statsEl) {
            const total = this.filteredCities.length;
            const totalAll = this.currentCities.length;
            
            if (total === totalAll) {
                statsEl.textContent = `共 ${total} 个城市`;
            } else {
                statsEl.textContent = `显示 ${total} 个城市 (共 ${totalAll} 个)`;
            }
        }
    },

    /**
     * 使用选定的城市填充表单
     */
    async useCityForForm(cityCode) {
        console.log('🏙️ 使用城市填充表单:', cityCode);
        
        if (!this.mdacValidator) {
            console.error('❌ MDACValidator未初始化');
            return;
        }
        
        // 获取城市详细信息
        const cityDetails = this.mdacValidator.getCityDetails(cityCode);
        if (!cityDetails) {
            console.error('❌ 未找到城市信息:', cityCode);
            return;
        }
        
        // 关闭城市查看器
        this.closeCityViewer();
        
        // 构造表单数据
        const formData = {
            city: cityDetails.name,
            state: this.mdacValidator.codeMappings.states[cityDetails.stateCode] || cityDetails.stateCode
        };
        
        // 如果有邮编，也添加到数据中
        if (cityDetails.postcodeRange && cityDetails.postcodeRange.length > 0) {
            formData.postcode = cityDetails.postcodeRange[0]; // 使用范围的第一个邮编
        }
        
        // 填充到表单
        await this.fillFormWithData(formData);
        
        // 显示成功消息
        this.showMessage(`✅ 已使用城市: ${cityDetails.name} (${cityDetails.chinese})`, 'success');
    },

    /**
     * 使用数据填充表单
     */
    async fillFormWithData(data) {
        try {
            // 首先填充侧边栏字段
            this.fillSidePanelFields(data);

            // 然后发送消息到内容脚本填充MDAC页面
            await chrome.tabs.sendMessage(this.currentTab.id, {
                action: 'fillForm',
                data: data
            });

            console.log('✅ 表单填充完成:', data);
        } catch (error) {
            console.error('❌ 表单填充失败:', error);
            this.showMessage('表单填充失败: ' + error.message, 'error');
        }
    },

    /**
     * 填充侧边栏字段
     */
    fillSidePanelFields(data) {
        console.log('🔄 填充侧边栏字段:', data);

        // 定义字段映射
        const fieldMappings = {
            // 个人信息字段
            'name': 'name',
            'passportNo': 'passportNo',
            'dateOfBirth': 'dateOfBirth',
            'nationality': 'nationality',
            'sex': 'sex',
            'passportExpiry': 'passportExpiry',
            'email': 'email',
            'confirmEmail': 'confirmEmail',
            'countryCode': 'countryCode',
            'mobileNo': 'mobileNo',

            // 旅行信息字段
            'arrivalDate': 'arrivalDate',
            'departureDate': 'departureDate',
            'flightNo': 'flightNo',
            'modeOfTravel': 'modeOfTravel',
            'lastPort': 'lastPort',
            'accommodation': 'accommodation',
            'address': 'address',
            'address2': 'address2',
            'state': 'state',
            'city': 'city',
            'postcode': 'postcode'
        };

        // 填充字段
        Object.entries(fieldMappings).forEach(([dataKey, fieldId]) => {
            if (data[dataKey] !== undefined && data[dataKey] !== null) {
                const field = document.getElementById(fieldId);
                if (field) {
                    if (field.tagName === 'SELECT') {
                        // 处理下拉框
                        this.setSelectValue(field, data[dataKey], fieldId);
                    } else {
                        // 处理普通输入框
                        field.value = data[dataKey];
                        console.log(`✅ 填充字段 ${fieldId}: ${data[dataKey]}`);
                    }
                }
            }
        });

        // 特殊处理：如果填充了州属，需要更新城市选项
        if (data.state) {
            setTimeout(() => {
                this.updateCityDropdown(data.state);
                // 如果有城市数据，再次设置城市值
                if (data.city) {
                    setTimeout(() => {
                        const cityField = document.getElementById('city');
                        if (cityField) {
                            this.setSelectValue(cityField, data.city, 'city');
                        }
                    }, 100);
                }
            }, 50);
        }
    },

    /**
     * 设置下拉框值
     */
    setSelectValue(selectField, value, fieldId) {
        // 尝试直接匹配值
        let option = Array.from(selectField.options).find(opt => opt.value === value);

        if (!option) {
            // 如果直接匹配失败，尝试智能匹配
            option = this.smartMatchSelectOption(selectField, value, fieldId);
        }

        if (option) {
            selectField.value = option.value;
            console.log(`✅ 下拉框字段 ${fieldId} 填充成功: ${option.value} (${option.text})`);

            // 触发change事件
            selectField.dispatchEvent(new Event('change', { bubbles: true }));
        } else {
            console.warn(`⚠️ 无法匹配下拉框选项 ${fieldId}: ${value}`);
        }
    },

    /**
     * 初始化旅行信息字段
     */
    initializeTravelInfoFields() {
        console.log('🏛️ 初始化旅行信息字段...');

        // 初始化州属下拉框
        this.initializeStateDropdown();

        console.log('✅ 旅行信息字段初始化完成');
    },

    /**
     * 初始化州属下拉框
     */
    async initializeStateDropdown() {
        const stateSelect = document.getElementById('state');
        if (!stateSelect) return;

        // 清空现有选项
        stateSelect.innerHTML = '<option value="">选择州属</option>';

        // 确保验证器数据已加载
        if (this.mdacValidator) {
            try {
                await this.mdacValidator.ensureDataLoaded();

                if (this.mdacValidator.codeMappings && this.mdacValidator.codeMappings.states) {
                    Object.entries(this.mdacValidator.codeMappings.states).forEach(([code, name]) => {
                        const option = document.createElement('option');
                        option.value = code;
                        option.textContent = name;
                        stateSelect.appendChild(option);
                    });
                    console.log('✅ 州属下拉框初始化完成');
                    return;
                }
            } catch (error) {
                console.error('❌ 加载州属数据失败:', error);
            }
        }

        // 降级方案：使用默认州属选项
        console.warn('⚠️ 使用默认州属选项');
        const defaultStates = [
            { code: '01', name: 'Johor' },
            { code: '07', name: 'Pulau Pinang' },
            { code: '14', name: 'Kuala Lumpur' },
            { code: '10', name: 'Selangor' },
            { code: '04', name: 'Melaka' },
            { code: '08', name: 'Perak' },
            { code: '12', name: 'Sabah' },
            { code: '13', name: 'Sarawak' }
        ];

        defaultStates.forEach(state => {
            const option = document.createElement('option');
            option.value = state.code;
            option.textContent = state.name;
            stateSelect.appendChild(option);
        });
    },

    /**
     * 处理州属选择变化
     */
    handleStateChange(event) {
        const stateCode = event.target.value;
        console.log('🏛️ 州属选择变化:', stateCode);

        // 更新城市下拉框
        this.updateCityDropdown(stateCode);

        // 清空邮编字段
        const postcodeField = document.getElementById('postcode');
        if (postcodeField) {
            postcodeField.value = '';
        }
    },

    /**
     * 更新城市下拉框
     */
    async updateCityDropdown(stateCode) {
        const citySelect = document.getElementById('city');
        if (!citySelect) return;

        // 清空现有选项
        citySelect.innerHTML = '<option value="">选择城市</option>';

        if (!stateCode) return;

        // 如果验证器可用，获取该州的城市
        if (this.mdacValidator) {
            try {
                await this.mdacValidator.ensureDataLoaded();
                const cities = this.mdacValidator.getCitiesByStateWithDetails(stateCode);

                if (cities && cities.length > 0) {
                    cities.forEach(city => {
                        const option = document.createElement('option');
                        option.value = city.code;
                        option.textContent = `${city.name} (${city.chinese})`;
                        citySelect.appendChild(option);
                    });
                    console.log(`✅ 已加载 ${cities.length} 个城市选项`);
                } else {
                    console.warn(`⚠️ 州属 ${stateCode} 没有找到城市数据`);
                }
            } catch (error) {
                console.error('❌ 加载城市数据失败:', error);
                // 添加一个错误提示选项
                const errorOption = document.createElement('option');
                errorOption.value = '';
                errorOption.textContent = '加载城市数据失败';
                errorOption.disabled = true;
                citySelect.appendChild(errorOption);
            }
        } else {
            console.warn('⚠️ 验证器未初始化，无法加载城市数据');
        }
    },

    /**
     * 处理城市选择变化
     */
    handleCityChange(event) {
        const cityCode = event.target.value;
        console.log('🏙️ 城市选择变化:', cityCode);

        if (!cityCode) return;

        // 自动填充邮编
        this.autoFillPostcode(cityCode);
    },

    /**
     * 自动填充邮编
     */
    async autoFillPostcode(cityCode) {
        const postcodeField = document.getElementById('postcode');
        if (!postcodeField || !this.mdacValidator) return;

        try {
            // 确保数据已加载
            await this.mdacValidator.ensureDataLoaded();

            // 获取城市详细信息
            const cityDetails = await this.mdacValidator.getCityDetails(cityCode);
            if (cityDetails) {
                let postcode = null;

                // 尝试从不同的数据格式中获取邮编
                if (cityDetails.postcodeRange && cityDetails.postcodeRange.length > 0) {
                    // 使用邮编范围的第一个值
                    postcode = cityDetails.postcodeRange[0];
                } else if (cityDetails.postcode && cityDetails.postcode !== 'N/A') {
                    // 从字符串格式中提取第一个邮编
                    if (cityDetails.postcode.includes('-')) {
                        postcode = cityDetails.postcode.split('-')[0];
                    } else {
                        postcode = cityDetails.postcode;
                    }
                }

                if (postcode) {
                    postcodeField.value = postcode;
                    console.log(`✅ 自动填充邮编: ${postcode} (来自城市: ${cityDetails.name})`);

                    // 添加视觉反馈
                    postcodeField.style.borderColor = '#28a745';
                    setTimeout(() => {
                        postcodeField.style.borderColor = '';
                    }, 2000);
                } else {
                    console.warn(`⚠️ 城市 ${cityCode} (${cityDetails.name}) 没有有效的邮编信息`);
                }
            } else {
                console.warn(`⚠️ 无法获取城市 ${cityCode} 的详细信息`);
            }
        } catch (error) {
            console.error('❌ 自动填充邮编失败:', error);
        }
    },

    /**
     * 处理邮编输入
     */
    handlePostcodeInput(event) {
        const postcode = event.target.value;
        console.log('📮 邮编输入:', postcode);

        // 验证邮编格式
        if (postcode.length === 5 && /^\d{5}$/.test(postcode)) {
            this.validatePostcode(postcode);
        }
    },

    /**
     * 验证邮编
     */
    async validatePostcode(postcode) {
        if (!this.mdacValidator) return;

        try {
            // 确保数据已加载
            await this.mdacValidator.ensureDataLoaded();

            const cityCode = this.mdacValidator.getCityByPostcode(postcode);
            const postcodeField = document.getElementById('postcode');

            if (cityCode) {
                const cityDetails = await this.mdacValidator.getCityDetails(cityCode);
                console.log(`✅ 邮编验证成功: ${postcode} → ${cityDetails.name}`);

                // 添加成功的视觉反馈
                if (postcodeField) {
                    postcodeField.style.borderColor = '#28a745';
                    postcodeField.title = `有效邮编 - ${cityDetails.name} (${cityDetails.chinese})`;
                }
            } else {
                console.warn(`⚠️ 邮编验证失败: ${postcode}`);

                // 添加失败的视觉反馈
                if (postcodeField) {
                    postcodeField.style.borderColor = '#dc3545';
                    postcodeField.title = '无效邮编';
                }
            }
        } catch (error) {
            console.error('❌ 邮编验证出错:', error);

            // 添加错误的视觉反馈
            const postcodeField = document.getElementById('postcode');
            if (postcodeField) {
                postcodeField.style.borderColor = '#ffc107';
                postcodeField.title = '邮编验证出错';
            }
        }
    }
});

// 初始化城市查看器（当页面加载完成后）
document.addEventListener('DOMContentLoaded', () => {
    if (window.mdacAssistant) {
        window.mdacAssistant.initializeCityViewer().catch(console.error);
    }
});

// ============================================================================
// 全局实例和初始化
// ============================================================================

// 创建全局实例
window.mdacAssistant = new MDACAssistantSidePanel();
