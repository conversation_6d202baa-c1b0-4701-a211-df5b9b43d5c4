/**
 * 住宿地址字段填充修复验证脚本
 * 用于测试解决方案1的实施效果
 */

console.log('🧪 开始测试住宿地址字段填充修复...');

// 测试数据
const testCases = [
    {
        name: '测试用例1: 新山乐高乐园',
        data: {
            address: 'Legoland Malaysia Resort',
            state: '01',
            city: '0118',
            postcode: '79100'
        }
    },
    {
        name: '测试用例2: accommodationAddress字段',
        data: {
            accommodationAddress: 'KUALA LUMPUR CITY CENTER HOTEL',
            accommodationState: '14',
            accommodationCity: '1400',
            accommodationPostcode: '50000'
        }
    },
    {
        name: '测试用例3: 两种字段名都存在',
        data: {
            address: 'Primary Address',
            accommodationAddress: 'Secondary Address',
            state: '07',
            city: '1000',
            postcode: '10000'
        }
    }
];

/**
 * 执行单个测试用例
 */
async function runTestCase(testCase) {
    console.log(`\n🔬 执行${testCase.name}...`);
    console.log('📊 测试数据:', testCase.data);
    
    // 清空地址字段
    const addressField = document.getElementById('address');
    if (addressField) {
        addressField.value = '';
        addressField.classList.remove('filled');
    }
    
    // 检查UI对象是否存在
    if (!window.ui || typeof window.ui.fillTravelFields !== 'function') {
        console.error('❌ UI对象或fillTravelFields方法不存在');
        return false;
    }
    
    try {
        // 调用修复后的fillTravelFields方法
        await window.ui.fillTravelFields(testCase.data);
        
        // 验证结果
        const expectedAddress = testCase.data.address || testCase.data.accommodationAddress;
        const actualAddress = addressField ? addressField.value : '';
        
        console.log('🔍 验证结果:');
        console.log('  期望地址:', expectedAddress);
        console.log('  实际地址:', actualAddress);
        console.log('  字段存在:', !!addressField);
        console.log('  填充成功:', actualAddress === expectedAddress);
        
        if (actualAddress === expectedAddress) {
            console.log('✅ 测试通过');
            return true;
        } else {
            console.log('❌ 测试失败');
            return false;
        }
        
    } catch (error) {
        console.error('❌ 测试执行异常:', error);
        return false;
    }
}

/**
 * 执行所有测试用例
 */
async function runAllTests() {
    console.log('🚀 开始执行所有测试用例...');
    
    let passedTests = 0;
    let totalTests = testCases.length;
    
    for (const testCase of testCases) {
        const result = await runTestCase(testCase);
        if (result) {
            passedTests++;
        }
        
        // 测试间隔
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log('\n📊 测试结果汇总:');
    console.log(`  总测试数: ${totalTests}`);
    console.log(`  通过测试: ${passedTests}`);
    console.log(`  失败测试: ${totalTests - passedTests}`);
    console.log(`  成功率: ${(passedTests / totalTests * 100).toFixed(1)}%`);
    
    if (passedTests === totalTests) {
        console.log('🎉 所有测试通过！住宿地址字段填充修复成功！');
    } else {
        console.log('⚠️ 部分测试失败，需要进一步调试');
    }
    
    return passedTests === totalTests;
}

/**
 * 手动测试函数
 */
function testAddressFilling(address = "新山乐高乐园") {
    console.log('🧪 手动测试地址填充:', address);
    
    const testData = {
        address: address,
        accommodationAddress: address,
        state: '01',
        city: '0118',
        postcode: '79100'
    };
    
    if (window.ui && window.ui.fillTravelFields) {
        window.ui.fillTravelFields(testData);
    } else {
        console.error('❌ UI对象不可用');
    }
}

// 导出测试函数
window.testAddressFillingFix = {
    runAllTests,
    runTestCase,
    testAddressFilling,
    testCases
};

console.log('✅ 住宿地址字段填充修复测试脚本已加载');
console.log('💡 使用方法:');
console.log('  - 运行所有测试: testAddressFillingFix.runAllTests()');
console.log('  - 手动测试: testAddressFillingFix.testAddressFilling("测试地址")');
