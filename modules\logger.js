/**
 * MDAC扩展日志管理系统
 * 提供统一的日志记录、管理和调试功能
 * 创建日期: 2025-01-11
 */

class MDACLogger {
    constructor() {
        this.logs = [];
        this.maxLogs = 1000; // 最大日志条数
        this.isEnabled = true;
        this.logLevel = 'DEBUG'; // DEBUG, INFO, WARN, ERROR
        this.listeners = new Set(); // 日志监听器
        this.performanceMarks = new Map(); // 性能标记
        
        // 日志级别优先级
        this.logLevels = {
            'DEBUG': 0,
            'INFO': 1,
            'WARN': 2,
            'ERROR': 3
        };
        
        // 模块颜色映射
        this.moduleColors = {
            'AI': '#2563eb',
            'FORM': '#059669',
            'UI': '#7c3aed',
            'NETWORK': '#dc2626',
            'VALIDATOR': '#ea580c',
            'SYSTEM': '#6b7280',
            'USER': '#0891b2'
        };
        
        console.log('🔧 MDACLogger初始化完成');
        this.loadSettings();
    }

    /**
     * 加载日志设置
     */
    async loadSettings() {
        try {
            const result = await chrome.storage.local.get(['loggerSettings']);
            if (result.loggerSettings) {
                const settings = result.loggerSettings;
                this.isEnabled = settings.enabled !== false;
                this.logLevel = settings.level || 'DEBUG';
                this.maxLogs = settings.maxLogs || 1000;
            }
        } catch (error) {
            console.warn('加载日志设置失败:', error);
        }
    }

    /**
     * 保存日志设置
     */
    async saveSettings() {
        try {
            await chrome.storage.local.set({
                loggerSettings: {
                    enabled: this.isEnabled,
                    level: this.logLevel,
                    maxLogs: this.maxLogs
                }
            });
        } catch (error) {
            console.warn('保存日志设置失败:', error);
        }
    }

    /**
     * 记录日志
     * @param {string} level - 日志级别 (DEBUG, INFO, WARN, ERROR)
     * @param {string} module - 模块名称
     * @param {string} message - 日志消息
     * @param {any} data - 附加数据
     */
    log(level, module, message, data = null) {
        if (!this.isEnabled) return;
        
        // 检查日志级别
        if (this.logLevels[level] < this.logLevels[this.logLevel]) {
            return;
        }
        
        const timestamp = new Date().toISOString();
        const logEntry = {
            id: Date.now() + Math.random(),
            timestamp,
            level,
            module,
            message,
            data,
            formattedTime: this.formatTime(timestamp)
        };
        
        // 添加到日志数组
        this.logs.push(logEntry);
        
        // 限制日志数量
        if (this.logs.length > this.maxLogs) {
            this.logs.shift();
        }
        
        // 输出到控制台
        this.outputToConsole(logEntry);
        
        // 通知监听器
        this.notifyListeners(logEntry);
    }

    /**
     * 输出到浏览器控制台
     */
    outputToConsole(logEntry) {
        const { level, module, message, data, formattedTime } = logEntry;
        const color = this.moduleColors[module] || '#6b7280';
        const prefix = `[${formattedTime}] [${module}] [${level}]`;
        
        const style = `color: ${color}; font-weight: bold;`;
        
        switch (level) {
            case 'DEBUG':
                console.log(`%c${prefix}`, style, message, data || '');
                break;
            case 'INFO':
                console.info(`%c${prefix}`, style, message, data || '');
                break;
            case 'WARN':
                console.warn(`%c${prefix}`, style, message, data || '');
                break;
            case 'ERROR':
                console.error(`%c${prefix}`, style, message, data || '');
                break;
        }
    }

    /**
     * 格式化时间
     */
    formatTime(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleTimeString('zh-CN', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            fractionalSecondDigits: 3
        });
    }

    /**
     * 便捷方法
     */
    debug(module, message, data) {
        this.log('DEBUG', module, message, data);
    }

    info(module, message, data) {
        this.log('INFO', module, message, data);
    }

    warn(module, message, data) {
        this.log('WARN', module, message, data);
    }

    error(module, message, data) {
        this.log('ERROR', module, message, data);
    }

    /**
     * 性能监控
     */
    startPerformance(operation) {
        const markName = `${operation}_start`;
        this.performanceMarks.set(operation, Date.now());
        this.debug('SYSTEM', `开始性能监控: ${operation}`);
    }

    endPerformance(operation) {
        const startTime = this.performanceMarks.get(operation);
        if (startTime) {
            const duration = Date.now() - startTime;
            this.performanceMarks.delete(operation);
            this.info('SYSTEM', `性能监控完成: ${operation}`, { duration: `${duration}ms` });
            return duration;
        }
        return null;
    }

    /**
     * 添加日志监听器
     */
    addListener(callback) {
        this.listeners.add(callback);
    }

    /**
     * 移除日志监听器
     */
    removeListener(callback) {
        this.listeners.delete(callback);
    }

    /**
     * 通知所有监听器
     */
    notifyListeners(logEntry) {
        this.listeners.forEach(callback => {
            try {
                callback(logEntry);
            } catch (error) {
                console.error('日志监听器错误:', error);
            }
        });
    }

    /**
     * 获取日志
     */
    getLogs(filter = {}) {
        let filteredLogs = [...this.logs];
        
        // 按级别过滤
        if (filter.level) {
            filteredLogs = filteredLogs.filter(log => log.level === filter.level);
        }
        
        // 按模块过滤
        if (filter.module) {
            filteredLogs = filteredLogs.filter(log => log.module === filter.module);
        }
        
        // 按时间范围过滤
        if (filter.startTime) {
            filteredLogs = filteredLogs.filter(log => 
                new Date(log.timestamp) >= new Date(filter.startTime)
            );
        }
        
        if (filter.endTime) {
            filteredLogs = filteredLogs.filter(log => 
                new Date(log.timestamp) <= new Date(filter.endTime)
            );
        }
        
        // 按关键词搜索
        if (filter.search) {
            const searchTerm = filter.search.toLowerCase();
            filteredLogs = filteredLogs.filter(log => 
                log.message.toLowerCase().includes(searchTerm) ||
                log.module.toLowerCase().includes(searchTerm)
            );
        }
        
        return filteredLogs;
    }

    /**
     * 清除日志
     */
    clearLogs() {
        this.logs = [];
        this.info('SYSTEM', '日志已清除');
    }

    /**
     * 导出日志
     */
    exportLogs(format = 'text') {
        const logs = this.getLogs();
        
        if (format === 'json') {
            return JSON.stringify(logs, null, 2);
        } else {
            return logs.map(log => {
                const dataStr = log.data ? ` | 数据: ${JSON.stringify(log.data)}` : '';
                return `[${log.formattedTime}] [${log.module}] [${log.level}] ${log.message}${dataStr}`;
            }).join('\n');
        }
    }

    /**
     * 下载日志文件
     */
    downloadLogs(filename = null) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const defaultFilename = `mdac-logs-${timestamp}.txt`;
        const finalFilename = filename || defaultFilename;
        
        const content = this.exportLogs('text');
        const blob = new Blob([content], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = finalFilename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.info('SYSTEM', `日志已导出: ${finalFilename}`);
    }

    /**
     * 设置日志级别
     */
    setLogLevel(level) {
        if (this.logLevels.hasOwnProperty(level)) {
            this.logLevel = level;
            this.saveSettings();
            this.info('SYSTEM', `日志级别已设置为: ${level}`);
        }
    }

    /**
     * 启用/禁用日志
     */
    setEnabled(enabled) {
        this.isEnabled = enabled;
        this.saveSettings();
        this.info('SYSTEM', `日志系统${enabled ? '已启用' : '已禁用'}`);
    }

    /**
     * 获取统计信息
     */
    getStats() {
        const stats = {
            total: this.logs.length,
            byLevel: {},
            byModule: {},
            timeRange: null
        };
        
        // 按级别统计
        Object.keys(this.logLevels).forEach(level => {
            stats.byLevel[level] = this.logs.filter(log => log.level === level).length;
        });
        
        // 按模块统计
        this.logs.forEach(log => {
            stats.byModule[log.module] = (stats.byModule[log.module] || 0) + 1;
        });
        
        // 时间范围
        if (this.logs.length > 0) {
            stats.timeRange = {
                start: this.logs[0].timestamp,
                end: this.logs[this.logs.length - 1].timestamp
            };
        }
        
        return stats;
    }
}

// 创建全局日志实例
window.mdacLogger = new MDACLogger();
