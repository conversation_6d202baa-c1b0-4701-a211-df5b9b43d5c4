/**
 * MDAC表单填充修复测试脚本
 * 用于验证数据收集和表单填充功能
 */

// 测试表单填充修复
function testFormFillingFix() {
    console.log('🧪 开始测试表单填充修复...');
    
    // 1. 创建测试数据
    console.log('\n📝 创建测试数据:');
    const testPersonalInfo = `
姓名: ZHANG WEI
护照号: A12345678
出生日期: 15/05/1990
国籍: CHN
性别: M
邮箱: <EMAIL>
电话: +60123456789
    `.trim();
    
    const testTravelInfo = `
航班号: MH123
交通方式: AIR
出发地: KUL
到达日期: 15/01/2025
离开日期: 20/01/2025
    `.trim();
    
    const testAccommodationInfo = `
住宿类型: 01
地址: LEGOLAND Malaysia Resort Hotel
州属: 01
城市: 0118
邮编: 79250
    `.trim();
    
    console.log('个人信息测试数据:', testPersonalInfo);
    console.log('旅行信息测试数据:', testTravelInfo);
    console.log('住宿信息测试数据:', testAccommodationInfo);
    
    // 2. 模拟填充侧边栏输入框
    console.log('\n📋 模拟填充侧边栏输入框:');
    
    function fillInputField(id, value) {
        const input = document.getElementById(id);
        if (input) {
            input.value = value;
            input.dispatchEvent(new Event('input', { bubbles: true }));
            console.log(`✅ ${id}: 已填充 (${value.length} 字符)`);
            return true;
        } else {
            console.log(`❌ ${id}: 输入框不存在`);
            return false;
        }
    }
    
    fillInputField('personalInfoInput', testPersonalInfo);
    fillInputField('travelInfoInput', testTravelInfo);
    fillInputField('accommodationInfoInput', testAccommodationInfo);
    
    // 3. 测试数据收集功能
    console.log('\n🔍 测试数据收集功能:');
    
    // 等待一下让输入事件处理完成
    setTimeout(() => {
        // 检查是否有collectAllFormData函数
        if (typeof collectAllFormData === 'function') {
            console.log('✅ 全局 collectAllFormData 函数存在');
            
            try {
                const collectedData = collectAllFormData();
                console.log('收集到的数据:', collectedData);
                console.log('数据字段数量:', Object.keys(collectedData).length);
                
                if (Object.keys(collectedData).length > 0) {
                    console.log('✅ 数据收集成功');
                    
                    // 显示收集到的字段
                    Object.entries(collectedData).forEach(([key, value]) => {
                        console.log(`  ${key}: "${value}"`);
                    });
                } else {
                    console.log('❌ 数据收集失败 - 返回空对象');
                }
                
            } catch (error) {
                console.log('❌ 数据收集出错:', error.message);
            }
            
        } else if (window.mdacUI && typeof window.mdacUI.collectAllFormData === 'function') {
            console.log('✅ mdacUI.collectAllFormData 函数存在');
            
            try {
                const collectedData = window.mdacUI.collectAllFormData();
                console.log('收集到的数据:', collectedData);
                console.log('数据字段数量:', Object.keys(collectedData).length);
                
                if (Object.keys(collectedData).length > 0) {
                    console.log('✅ 数据收集成功');
                    
                    // 显示收集到的字段
                    Object.entries(collectedData).forEach(([key, value]) => {
                        console.log(`  ${key}: "${value}"`);
                    });
                    
                    // 4. 测试表单填充功能
                    console.log('\n🚀 测试表单填充功能:');
                    
                    if (typeof window.mdacUI.updateToMDAC === 'function') {
                        console.log('准备调用 updateToMDAC...');
                        
                        // 模拟点击填充按钮
                        setTimeout(() => {
                            try {
                                window.mdacUI.updateToMDAC();
                                console.log('✅ updateToMDAC 调用成功');
                            } catch (error) {
                                console.log('❌ updateToMDAC 调用失败:', error.message);
                            }
                        }, 1000);
                        
                    } else {
                        console.log('❌ updateToMDAC 函数不存在');
                    }
                    
                } else {
                    console.log('❌ 数据收集失败 - 返回空对象');
                }
                
            } catch (error) {
                console.log('❌ 数据收集出错:', error.message);
            }
            
        } else {
            console.log('❌ collectAllFormData 函数不存在');
        }
        
    }, 500);
    
    // 5. 检查日志记录
    console.log('\n📊 检查日志记录:');
    setTimeout(() => {
        if (window.mdacLogger) {
            const recentLogs = window.mdacLogger.getLogs({
                startTime: new Date(Date.now() - 2*60*1000).toISOString() // 最近2分钟
            });
            
            console.log(`最近2分钟的日志数量: ${recentLogs.length}`);
            
            // 显示表单相关日志
            const formLogs = recentLogs.filter(log => log.module === 'FORM');
            console.log(`表单相关日志数量: ${formLogs.length}`);
            
            if (formLogs.length > 0) {
                console.log('最近的表单日志:');
                formLogs.slice(-10).forEach(log => {
                    console.log(`[${log.formattedTime}] [${log.level}] ${log.message}`);
                });
            }
        } else {
            console.log('❌ mdacLogger 不存在');
        }
    }, 2000);
    
    // 6. 验证表单字段是否被填充
    console.log('\n🔍 验证表单字段填充状态:');
    setTimeout(() => {
        const keyFields = [
            'name', 'passNo', 'dob', 'nationality', 'sex', 'email', 
            'vesselNm', 'trvlMode', 'embark',
            'accommodationStay', 'accommodationAddress1', 'accommodationState', 'accommodationCity', 'accommodationPostcode'
        ];
        
        let filledCount = 0;
        keyFields.forEach(fieldId => {
            const element = document.getElementById(fieldId);
            if (element && element.value && element.value.trim()) {
                console.log(`✅ ${fieldId}: "${element.value}"`);
                filledCount++;
            } else {
                console.log(`❌ ${fieldId}: 未填充或不存在`);
            }
        });
        
        console.log(`\n📊 填充结果: ${filledCount}/${keyFields.length} 个字段已填充`);
        
        if (filledCount > 0) {
            console.log('🎉 表单填充功能正常工作！');
        } else {
            console.log('⚠️ 表单填充可能存在问题，需要进一步调试');
        }
        
    }, 3000);
    
    // 7. 最终报告
    setTimeout(() => {
        console.log('\n📋 测试完成报告:');
        
        const testResults = {
            dataInput: true, // 测试数据输入
            dataCollection: false, // 待验证
            formFilling: false, // 待验证
            logging: window.mdacLogger ? true : false
        };
        
        // 检查数据收集结果
        if (typeof collectAllFormData === 'function' || 
            (window.mdacUI && typeof window.mdacUI.collectAllFormData === 'function')) {
            testResults.dataCollection = true;
        }
        
        // 检查表单填充结果
        const nameField = document.getElementById('name');
        if (nameField && nameField.value && nameField.value.trim()) {
            testResults.formFilling = true;
        }
        
        const passedTests = Object.values(testResults).filter(result => result).length;
        const totalTests = Object.keys(testResults).length;
        
        console.log(`测试通过率: ${passedTests}/${totalTests} (${((passedTests/totalTests)*100).toFixed(1)}%)`);
        console.log('详细结果:', testResults);
        
        if (passedTests === totalTests) {
            console.log('🎉 所有测试通过！表单填充修复成功。');
        } else {
            console.log('⚠️ 部分测试未通过，需要进一步修复。');
        }
        
    }, 4000);
}

// 自动运行测试（如果在浏览器环境中）
if (typeof window !== 'undefined' && document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(testFormFillingFix, 2000);
    });
} else if (typeof window !== 'undefined') {
    setTimeout(testFormFillingFix, 2000);
}

// 导出测试函数（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { testFormFillingFix };
}
