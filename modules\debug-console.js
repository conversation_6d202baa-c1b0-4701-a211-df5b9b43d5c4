/**
 * MDAC调试控制台组件
 * 提供实时日志显示、过滤和管理功能
 * 创建日期: 2025-01-11
 */

class MDACDebugConsole {
    constructor() {
        this.isVisible = false;
        this.isPaused = false;
        this.autoScroll = true;
        this.currentFilter = {};
        this.container = null;
        this.logContainer = null;
        this.maxDisplayLogs = 500; // 最大显示日志数
        
        console.log('🔧 MDACDebugConsole初始化...');
        this.init();
    }

    /**
     * 初始化调试控制台
     */
    init() {
        this.createConsoleUI();
        this.setupEventListeners();
        this.loadSettings();
        
        // 监听日志更新
        if (window.mdacLogger) {
            window.mdacLogger.addListener((logEntry) => {
                if (!this.isPaused) {
                    this.addLogEntry(logEntry);
                }
            });
        }
    }

    /**
     * 创建控制台UI
     */
    createConsoleUI() {
        // 检查是否已存在
        if (document.getElementById('mdac-debug-console')) return;

        const consoleHTML = `
            <div id="mdac-debug-console" class="debug-console" style="display: none;">
                <div class="debug-console-header">
                    <div class="console-title">
                        <span class="console-icon">🐛</span>
                        <span class="console-text">调试控制台</span>
                        <span class="log-count" id="logCount">0 条日志</span>
                    </div>
                    <div class="console-controls">
                        <button class="console-btn" id="pauseBtn" title="暂停/恢复日志">⏸️</button>
                        <button class="console-btn" id="clearBtn" title="清除日志">🗑️</button>
                        <button class="console-btn" id="exportBtn" title="导出日志">💾</button>
                        <button class="console-btn" id="settingsBtn" title="设置">⚙️</button>
                        <button class="console-btn" id="minimizeBtn" title="最小化">➖</button>
                    </div>
                </div>
                
                <div class="debug-console-toolbar">
                    <div class="filter-section">
                        <select id="levelFilter" class="filter-select">
                            <option value="">所有级别</option>
                            <option value="DEBUG">DEBUG</option>
                            <option value="INFO">INFO</option>
                            <option value="WARN">WARN</option>
                            <option value="ERROR">ERROR</option>
                        </select>
                        
                        <select id="moduleFilter" class="filter-select">
                            <option value="">所有模块</option>
                            <option value="AI">AI</option>
                            <option value="FORM">FORM</option>
                            <option value="UI">UI</option>
                            <option value="NETWORK">NETWORK</option>
                            <option value="VALIDATOR">VALIDATOR</option>
                            <option value="SYSTEM">SYSTEM</option>
                            <option value="USER">USER</option>
                        </select>
                        
                        <input type="text" id="searchInput" class="search-input" placeholder="搜索日志...">
                        <button class="filter-btn" id="filterBtn">🔍</button>
                        <button class="filter-btn" id="clearFilterBtn">✖️</button>
                    </div>
                    
                    <div class="view-options">
                        <label class="checkbox-label">
                            <input type="checkbox" id="autoScrollCheck" checked>
                            <span>自动滚动</span>
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="timestampCheck" checked>
                            <span>显示时间</span>
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="detailModeCheck">
                            <span>详细模式</span>
                        </label>
                    </div>
                </div>
                
                <div class="debug-console-content">
                    <div class="log-container" id="logContainer">
                        <div class="log-placeholder">
                            <span class="placeholder-icon">📝</span>
                            <span class="placeholder-text">等待日志输出...</span>
                        </div>
                    </div>
                </div>
                
                <div class="debug-console-footer">
                    <div class="stats-info" id="statsInfo">
                        <span>总计: 0</span>
                        <span>错误: 0</span>
                        <span>警告: 0</span>
                    </div>
                    <div class="performance-info" id="performanceInfo">
                        内存使用: --
                    </div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', consoleHTML);
        
        this.container = document.getElementById('mdac-debug-console');
        this.logContainer = document.getElementById('logContainer');
        
        // 添加样式
        this.addConsoleStyles();
    }

    /**
     * 添加控制台样式
     */
    addConsoleStyles() {
        if (document.getElementById('debug-console-styles')) return;

        const styles = `
            <style id="debug-console-styles">
                .debug-console {
                    position: fixed;
                    bottom: 20px;
                    right: 20px;
                    width: 600px;
                    height: 400px;
                    background: #1e293b;
                    border: 1px solid #334155;
                    border-radius: 8px;
                    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
                    z-index: 10000;
                    font-family: 'Consolas', 'Monaco', monospace;
                    font-size: 12px;
                    color: #e2e8f0;
                    display: flex;
                    flex-direction: column;
                    resize: both;
                    overflow: hidden;
                    min-width: 400px;
                    min-height: 300px;
                }

                .debug-console-header {
                    background: #0f172a;
                    padding: 8px 12px;
                    border-bottom: 1px solid #334155;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    cursor: move;
                }

                .console-title {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    font-weight: bold;
                }

                .log-count {
                    background: #374151;
                    padding: 2px 6px;
                    border-radius: 4px;
                    font-size: 10px;
                }

                .console-controls {
                    display: flex;
                    gap: 4px;
                }

                .console-btn {
                    background: #374151;
                    border: none;
                    padding: 4px 8px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 12px;
                    transition: background 0.2s;
                }

                .console-btn:hover {
                    background: #4b5563;
                }

                .debug-console-toolbar {
                    background: #0f172a;
                    padding: 8px 12px;
                    border-bottom: 1px solid #334155;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 8px;
                }

                .filter-section {
                    display: flex;
                    gap: 8px;
                    align-items: center;
                }

                .filter-select, .search-input {
                    background: #374151;
                    border: 1px solid #4b5563;
                    color: #e2e8f0;
                    padding: 4px 8px;
                    border-radius: 4px;
                    font-size: 11px;
                }

                .search-input {
                    width: 150px;
                }

                .filter-btn {
                    background: #374151;
                    border: 1px solid #4b5563;
                    color: #e2e8f0;
                    padding: 4px 8px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 11px;
                }

                .view-options {
                    display: flex;
                    gap: 12px;
                    align-items: center;
                }

                .checkbox-label {
                    display: flex;
                    align-items: center;
                    gap: 4px;
                    font-size: 11px;
                    cursor: pointer;
                }

                .debug-console-content {
                    flex: 1;
                    overflow: hidden;
                    background: #1e293b;
                }

                .log-container {
                    height: 100%;
                    overflow-y: auto;
                    padding: 8px;
                }

                .log-placeholder {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    height: 100%;
                    color: #64748b;
                    gap: 8px;
                }

                .log-entry {
                    margin-bottom: 2px;
                    padding: 4px 8px;
                    border-radius: 4px;
                    border-left: 3px solid transparent;
                    font-family: 'Consolas', 'Monaco', monospace;
                    font-size: 11px;
                    line-height: 1.4;
                    word-break: break-all;
                }

                .log-entry.DEBUG {
                    border-left-color: #64748b;
                    background: rgba(100, 116, 139, 0.1);
                }

                .log-entry.INFO {
                    border-left-color: #3b82f6;
                    background: rgba(59, 130, 246, 0.1);
                }

                .log-entry.WARN {
                    border-left-color: #f59e0b;
                    background: rgba(245, 158, 11, 0.1);
                }

                .log-entry.ERROR {
                    border-left-color: #ef4444;
                    background: rgba(239, 68, 68, 0.1);
                }

                .log-timestamp {
                    color: #64748b;
                    margin-right: 8px;
                }

                .log-module {
                    font-weight: bold;
                    margin-right: 8px;
                    padding: 1px 4px;
                    border-radius: 2px;
                    font-size: 10px;
                }

                .log-level {
                    font-weight: bold;
                    margin-right: 8px;
                    padding: 1px 4px;
                    border-radius: 2px;
                    font-size: 10px;
                }

                .log-message {
                    color: #e2e8f0;
                }

                .log-data {
                    color: #94a3b8;
                    font-style: italic;
                    margin-left: 8px;
                }

                .debug-console-footer {
                    background: #0f172a;
                    padding: 6px 12px;
                    border-top: 1px solid #334155;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    font-size: 10px;
                    color: #64748b;
                }

                .stats-info {
                    display: flex;
                    gap: 12px;
                }

                /* 响应式设计 */
                @media (max-width: 768px) {
                    .debug-console {
                        width: 90vw;
                        height: 50vh;
                        bottom: 10px;
                        right: 5vw;
                    }
                    
                    .debug-console-toolbar {
                        flex-direction: column;
                        align-items: stretch;
                    }
                    
                    .filter-section {
                        flex-wrap: wrap;
                    }
                }
            </style>
        `;

        document.head.insertAdjacentHTML('beforeend', styles);
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 暂停/恢复按钮
        document.getElementById('pauseBtn')?.addEventListener('click', () => {
            this.togglePause();
        });

        // 清除日志按钮
        document.getElementById('clearBtn')?.addEventListener('click', () => {
            this.clearLogs();
        });

        // 导出日志按钮
        document.getElementById('exportBtn')?.addEventListener('click', () => {
            this.exportLogs();
        });

        // 最小化按钮
        document.getElementById('minimizeBtn')?.addEventListener('click', () => {
            this.hide();
        });

        // 过滤器
        document.getElementById('levelFilter')?.addEventListener('change', (e) => {
            this.currentFilter.level = e.target.value;
            this.applyFilter();
        });

        document.getElementById('moduleFilter')?.addEventListener('change', (e) => {
            this.currentFilter.module = e.target.value;
            this.applyFilter();
        });

        document.getElementById('searchInput')?.addEventListener('input', (e) => {
            this.currentFilter.search = e.target.value;
            this.applyFilter();
        });

        document.getElementById('filterBtn')?.addEventListener('click', () => {
            this.applyFilter();
        });

        document.getElementById('clearFilterBtn')?.addEventListener('click', () => {
            this.clearFilter();
        });

        // 视图选项
        document.getElementById('autoScrollCheck')?.addEventListener('change', (e) => {
            this.autoScroll = e.target.checked;
        });

        // 拖拽功能
        this.makeDraggable();
    }

    /**
     * 添加日志条目
     */
    addLogEntry(logEntry) {
        if (!this.logContainer) return;

        // 移除占位符
        const placeholder = this.logContainer.querySelector('.log-placeholder');
        if (placeholder) {
            placeholder.remove();
        }

        // 创建日志元素
        const logElement = this.createLogElement(logEntry);
        this.logContainer.appendChild(logElement);

        // 限制显示的日志数量
        const logEntries = this.logContainer.querySelectorAll('.log-entry');
        if (logEntries.length > this.maxDisplayLogs) {
            logEntries[0].remove();
        }

        // 自动滚动
        if (this.autoScroll) {
            this.logContainer.scrollTop = this.logContainer.scrollHeight;
        }

        // 更新统计
        this.updateStats();
    }

    /**
     * 创建日志元素
     */
    createLogElement(logEntry) {
        const { level, module, message, data, formattedTime } = logEntry;
        
        const logDiv = document.createElement('div');
        logDiv.className = `log-entry ${level}`;
        
        const showTimestamp = document.getElementById('timestampCheck')?.checked !== false;
        const detailMode = document.getElementById('detailModeCheck')?.checked === true;
        
        let html = '';
        
        if (showTimestamp) {
            html += `<span class="log-timestamp">${formattedTime}</span>`;
        }
        
        html += `<span class="log-module" style="background: ${this.getModuleColor(module)}">${module}</span>`;
        html += `<span class="log-level">${level}</span>`;
        html += `<span class="log-message">${message}</span>`;
        
        if (data && detailMode) {
            const dataStr = typeof data === 'object' ? JSON.stringify(data, null, 2) : data;
            html += `<span class="log-data">${dataStr}</span>`;
        }
        
        logDiv.innerHTML = html;
        return logDiv;
    }

    /**
     * 获取模块颜色
     */
    getModuleColor(module) {
        const colors = {
            'AI': '#2563eb',
            'FORM': '#059669',
            'UI': '#7c3aed',
            'NETWORK': '#dc2626',
            'VALIDATOR': '#ea580c',
            'SYSTEM': '#6b7280',
            'USER': '#0891b2'
        };
        return colors[module] || '#6b7280';
    }

    /**
     * 应用过滤器
     */
    applyFilter() {
        if (!window.mdacLogger) return;

        const filteredLogs = window.mdacLogger.getLogs(this.currentFilter);
        this.refreshLogDisplay(filteredLogs);
    }

    /**
     * 刷新日志显示
     */
    refreshLogDisplay(logs) {
        if (!this.logContainer) return;

        this.logContainer.innerHTML = '';
        
        if (logs.length === 0) {
            this.logContainer.innerHTML = `
                <div class="log-placeholder">
                    <span class="placeholder-icon">🔍</span>
                    <span class="placeholder-text">没有匹配的日志</span>
                </div>
            `;
            return;
        }

        logs.slice(-this.maxDisplayLogs).forEach(logEntry => {
            const logElement = this.createLogElement(logEntry);
            this.logContainer.appendChild(logElement);
        });

        if (this.autoScroll) {
            this.logContainer.scrollTop = this.logContainer.scrollHeight;
        }

        this.updateStats();
    }

    /**
     * 清除过滤器
     */
    clearFilter() {
        this.currentFilter = {};
        document.getElementById('levelFilter').value = '';
        document.getElementById('moduleFilter').value = '';
        document.getElementById('searchInput').value = '';
        this.applyFilter();
    }

    /**
     * 切换暂停状态
     */
    togglePause() {
        this.isPaused = !this.isPaused;
        const pauseBtn = document.getElementById('pauseBtn');
        if (pauseBtn) {
            pauseBtn.textContent = this.isPaused ? '▶️' : '⏸️';
            pauseBtn.title = this.isPaused ? '恢复日志' : '暂停日志';
        }
    }

    /**
     * 清除日志
     */
    clearLogs() {
        if (window.mdacLogger) {
            window.mdacLogger.clearLogs();
        }
        
        if (this.logContainer) {
            this.logContainer.innerHTML = `
                <div class="log-placeholder">
                    <span class="placeholder-icon">📝</span>
                    <span class="placeholder-text">等待日志输出...</span>
                </div>
            `;
        }
        
        this.updateStats();
    }

    /**
     * 导出日志
     */
    exportLogs() {
        if (window.mdacLogger) {
            window.mdacLogger.downloadLogs();
        }
    }

    /**
     * 更新统计信息
     */
    updateStats() {
        if (!window.mdacLogger) return;

        const stats = window.mdacLogger.getStats();
        
        // 更新日志计数
        const logCount = document.getElementById('logCount');
        if (logCount) {
            logCount.textContent = `${stats.total} 条日志`;
        }

        // 更新统计信息
        const statsInfo = document.getElementById('statsInfo');
        if (statsInfo) {
            statsInfo.innerHTML = `
                <span>总计: ${stats.total}</span>
                <span>错误: ${stats.byLevel.ERROR || 0}</span>
                <span>警告: ${stats.byLevel.WARN || 0}</span>
            `;
        }

        // 更新性能信息
        const performanceInfo = document.getElementById('performanceInfo');
        if (performanceInfo && performance.memory) {
            const memoryMB = (performance.memory.usedJSHeapSize / 1024 / 1024).toFixed(1);
            performanceInfo.textContent = `内存使用: ${memoryMB}MB`;
        }
    }

    /**
     * 显示控制台
     */
    show() {
        if (this.container) {
            this.container.style.display = 'flex';
            this.isVisible = true;
            this.updateStats();
        }
    }

    /**
     * 隐藏控制台
     */
    hide() {
        if (this.container) {
            this.container.style.display = 'none';
            this.isVisible = false;
        }
    }

    /**
     * 切换显示状态
     */
    toggle() {
        if (this.isVisible) {
            this.hide();
        } else {
            this.show();
        }
    }

    /**
     * 使控制台可拖拽
     */
    makeDraggable() {
        const header = this.container?.querySelector('.debug-console-header');
        if (!header) return;

        let isDragging = false;
        let currentX;
        let currentY;
        let initialX;
        let initialY;
        let xOffset = 0;
        let yOffset = 0;

        header.addEventListener('mousedown', (e) => {
            initialX = e.clientX - xOffset;
            initialY = e.clientY - yOffset;

            if (e.target === header || header.contains(e.target)) {
                isDragging = true;
            }
        });

        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                e.preventDefault();
                currentX = e.clientX - initialX;
                currentY = e.clientY - initialY;

                xOffset = currentX;
                yOffset = currentY;

                this.container.style.transform = `translate3d(${currentX}px, ${currentY}px, 0)`;
            }
        });

        document.addEventListener('mouseup', () => {
            initialX = currentX;
            initialY = currentY;
            isDragging = false;
        });
    }

    /**
     * 加载设置
     */
    async loadSettings() {
        try {
            const result = await chrome.storage.local.get(['debugConsoleSettings']);
            if (result.debugConsoleSettings) {
                const settings = result.debugConsoleSettings;
                this.isVisible = settings.visible || false;
                this.autoScroll = settings.autoScroll !== false;
                
                if (this.isVisible) {
                    this.show();
                }
            }
        } catch (error) {
            console.warn('加载调试控制台设置失败:', error);
        }
    }

    /**
     * 保存设置
     */
    async saveSettings() {
        try {
            await chrome.storage.local.set({
                debugConsoleSettings: {
                    visible: this.isVisible,
                    autoScroll: this.autoScroll
                }
            });
        } catch (error) {
            console.warn('保存调试控制台设置失败:', error);
        }
    }
}

// 创建全局调试控制台实例
window.mdacDebugConsole = new MDACDebugConsole();
