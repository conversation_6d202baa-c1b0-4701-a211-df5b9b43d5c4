/**
 * MDAC AI智能填充工具 - 内容脚本
 * 在MDAC官网页面中注入，处理表单填充和页面交互
 */

class MDACContentScript {
    constructor() {
        this.isInitialized = false;
        this.formData = {};
        this.aiAssistant = null;
        this.fieldDetector = null;
        this.detectedFields = {};
        this.errorRecoveryManager = null;
        this.fillMonitor = null;
        this.progressVisualizer = null;
        this.currentFillSession = null;

        this.init();
    }

    /**
     * 初始化内容脚本
     */
    async init() {
        if (this.isInitialized) return;

        // 等待页面完全加载
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }

    /**
     * 设置内容脚本
     */
    async setup() {
        try {
            this.detectPageType();

            // 初始化智能字段检测器
            await this.initializeFieldDetector();

            // 初始化错误恢复管理器
            await this.initializeErrorRecoveryManager();

            // 初始化填充监控器
            await this.initializeFillMonitor();

            // 初始化进度可视化器
            await this.initializeProgressVisualizer();

            // 检测表单字段
            await this.detectFormFields();

            this.setupMessageListener();
            this.injectAIAssistant();
            this.observeFormChanges();
            this.isInitialized = true;

            console.log('MDAC AI内容脚本已初始化');
            this.notifyExtension('contentScriptReady');
        } catch (error) {
            console.error('内容脚本初始化失败:', error);
        }
    }

    /**
     * 初始化智能字段检测器
     */
    async initializeFieldDetector() {
        try {
            // 动态加载字段检测器
            if (typeof FormFieldDetector === 'undefined') {
                await this.loadScript(chrome.runtime.getURL('modules/form-field-detector.js'));
            }

            this.fieldDetector = new FormFieldDetector();
            console.log('✅ 智能字段检测器初始化完成');
        } catch (error) {
            console.error('❌ 字段检测器初始化失败:', error);
            // 降级到传统检测方法
            this.fieldDetector = null;
        }
    }

    /**
     * 动态加载脚本
     */
    loadScript(src) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    /**
     * 初始化错误恢复管理器
     */
    async initializeErrorRecoveryManager() {
        try {
            // 动态加载错误恢复管理器
            if (typeof ErrorRecoveryManager === 'undefined') {
                await this.loadScript(chrome.runtime.getURL('modules/error-recovery-manager.js'));
            }

            this.errorRecoveryManager = new ErrorRecoveryManager();
            await this.errorRecoveryManager.loadErrorHistory();
            console.log('✅ 错误恢复管理器初始化完成');
        } catch (error) {
            console.error('❌ 错误恢复管理器初始化失败:', error);
            // 不阻止其他功能的初始化
            this.errorRecoveryManager = null;
        }
    }

    /**
     * 初始化填充监控器
     */
    async initializeFillMonitor() {
        try {
            // 动态加载填充监控器
            if (typeof FillMonitor === 'undefined') {
                await this.loadScript(chrome.runtime.getURL('modules/fill-monitor.js'));
            }

            this.fillMonitor = new FillMonitor();
            console.log('✅ 填充监控器初始化完成');
        } catch (error) {
            console.error('❌ 填充监控器初始化失败:', error);
            // 不阻止其他功能的初始化
            this.fillMonitor = null;
        }
    }

    /**
     * 初始化进度可视化器
     */
    async initializeProgressVisualizer() {
        try {
            // 动态加载进度可视化器
            if (typeof ProgressVisualizer === 'undefined') {
                await this.loadScript(chrome.runtime.getURL('modules/progress-visualizer.js'));
            }

            this.progressVisualizer = new ProgressVisualizer();
            console.log('✅ 进度可视化器初始化完成');
        } catch (error) {
            console.error('❌ 进度可视化器初始化失败:', error);
            // 不阻止其他功能的初始化
            this.progressVisualizer = null;
        }
    }

    /**
     * 检测页面类型
     */
    detectPageType() {
        const url = window.location.href;
        
        if (url.includes('registerMain')) {
            this.pageType = 'registration';
            this.setupRegistrationPage();
        } else if (url.includes('confirmation')) {
            this.pageType = 'confirmation';
        } else {
            this.pageType = 'unknown';
        }
    }

    /**
     * 设置注册页面
     */
    setupRegistrationPage() {
        // 检查表单是否存在
        const form = document.querySelector('form[name="permohonan"]');
        if (form) {
            this.form = form;
            this.identifyFormFields();
            this.addFormValidation();
        }
    }

    /**
     * 智能检测表单字段
     */
    async detectFormFields() {
        console.log('🔍 开始智能表单字段检测...');

        if (this.fieldDetector) {
            try {
                // 使用智能检测器
                this.detectedFields = await this.fieldDetector.detectFormFields();

                // 验证检测结果
                const validation = this.fieldDetector.validateDetection(this.detectedFields);
                console.log('📊 字段检测验证结果:', validation);

                // 如果检测成功，使用检测结果
                if (validation.isValid || Object.keys(this.detectedFields).length > 5) {
                    this.formFields = this.detectedFields;
                    console.log(`✅ 智能检测成功，发现 ${Object.keys(this.detectedFields).length} 个字段`);

                    // 显示检测统计
                    const stats = this.fieldDetector.getDetectionStats();
                    console.log('📈 检测统计:', stats);

                    // 显示检测结果UI
                    this.showFieldDetectionStatus(validation, stats);

                    return;
                }
            } catch (error) {
                console.error('❌ 智能字段检测失败:', error);
            }
        }

        // 降级到传统检测方法
        console.log('🔄 降级到传统字段检测方法');
        this.identifyFormFields();
    }

    /**
     * 识别表单字段（传统方法）
     */
    identifyFormFields() {
        this.formFields = {
            // 个人信息 - 使用实际的字段 ID
            name: document.getElementById('name'),
            passNo: document.getElementById('passNo'),
            dob: document.getElementById('dob'),
            nationality: document.getElementById('nationality'),
            sex: document.getElementById('sex'),
            passExpiry: document.getElementById('passExpDte'), // 修正字段 ID
            email: document.getElementById('email'),
            confirmEmail: document.getElementById('confirmEmail'),
            countryCode: document.getElementById('region'), // 修正字段 ID
            mobileNo: document.getElementById('mobile'), // 修正字段 ID
            
            // 旅行信息 - 使用实际的字段 ID
            arrivalDate: document.getElementById('arrDt'), // 修正字段 ID
            departureDate: document.getElementById('depDt'), // 修正字段 ID
            vesselNm: document.getElementById('vesselNm'),
            trvlMode: document.getElementById('trvlMode'),
            embark: document.getElementById('embark'),
            
            // 住宿信息
            accommodationStay: document.getElementById('accommodationStay'),
            accommodationAddress1: document.getElementById('accommodationAddress1'),
            accommodationAddress2: document.getElementById('accommodationAddress2'),
            accommodationState: document.getElementById('accommodationState'),
            accommodationPostcode: document.getElementById('accommodationPostcode'),
            accommodationCity: document.getElementById('accommodationCity'),
            
            // 提交按钮
            submitBtn: document.getElementById('submit'),
            resetBtn: document.getElementById('reset')
        };

        // 过滤掉不存在的字段
        Object.keys(this.formFields).forEach(key => {
            if (!this.formFields[key]) {
                delete this.formFields[key];
            }
        });

        console.log(`📋 传统检测完成，发现 ${Object.keys(this.formFields).length} 个字段`);
        console.log('🔍 检测到的字段详情:', Object.keys(this.formFields));
        
        // 详细输出每个字段的状态
        Object.keys(this.formFields).forEach(key => {
            const field = this.formFields[key];
            console.log(`  - ${key}: ${field ? '✅ 存在' : '❌ 不存在'} (${field ? field.tagName : 'null'})`);
        });
    }

    /**
     * 添加表单验证功能
     */
    addFormValidation() {
        if (!this.form || !this.formFields) {
            console.warn('⚠️ 表单或字段未初始化，跳过验证设置');
            return;
        }

        console.log('🔧 正在添加表单验证功能...');

        // 为必填字段添加验证
        const requiredFields = [
            'name', 'passNo', 'dob', 'nationality', 'sex', 'passExpiry',
            'email', 'confirmEmail', 'countryCode', 'mobileNo',
            'arrivalDate', 'departureDate', 'accommodationStay'
        ];

        requiredFields.forEach(fieldKey => {
            const field = this.formFields[fieldKey];
            if (field) {
                // 添加必填标识
                this.markFieldAsRequired(field);

                // 添加实时验证
                field.addEventListener('blur', () => this.validateField(fieldKey, field));
                field.addEventListener('input', () => this.clearFieldError(field));
            }
        });

        // 添加表单提交验证
        this.form.addEventListener('submit', (e) => this.validateFormOnSubmit(e));

        console.log('✅ 表单验证功能已添加');
    }

    /**
     * 标记字段为必填
     */
    markFieldAsRequired(field) {
        if (!field) return;

        // 添加必填样式
        field.classList.add('mdac-required-field');

        // 查找标签并添加红色星号
        const label = document.querySelector(`label[for="${field.id}"]`);
        if (label && !label.querySelector('.required-asterisk')) {
            const asterisk = document.createElement('span');
            asterisk.className = 'required-asterisk';
            asterisk.textContent = ' *';
            asterisk.style.color = 'red';
            label.appendChild(asterisk);
        }
    }

    /**
     * 验证单个字段
     */
    validateField(fieldKey, field) {
        if (!field) return true;

        const value = field.value.trim();
        let isValid = true;
        let errorMessage = '';

        // 基础必填验证
        if (!value) {
            isValid = false;
            errorMessage = '此字段为必填项';
        } else {
            // 特定字段验证
            switch (fieldKey) {
                case 'email':
                case 'confirmEmail':
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(value)) {
                        isValid = false;
                        errorMessage = '请输入有效的邮箱地址';
                    }
                    break;
                case 'passNo':
                    if (value.length < 6) {
                        isValid = false;
                        errorMessage = '护照号码长度不足';
                    }
                    break;
                case 'mobileNo':
                    const phoneRegex = /^\d{7,15}$/;
                    if (!phoneRegex.test(value)) {
                        isValid = false;
                        errorMessage = '请输入有效的手机号码';
                    }
                    break;
            }
        }

        // 显示验证结果
        if (isValid) {
            this.clearFieldError(field);
        } else {
            this.showFieldError(field, errorMessage);
        }

        return isValid;
    }

    /**
     * 显示字段错误
     */
    showFieldError(field, message) {
        this.clearFieldError(field);

        field.classList.add('mdac-field-error');

        const errorDiv = document.createElement('div');
        errorDiv.className = 'mdac-field-error-message';
        errorDiv.textContent = message;
        errorDiv.style.cssText = 'color: red; font-size: 12px; margin-top: 4px;';

        field.parentNode.insertBefore(errorDiv, field.nextSibling);
    }

    /**
     * 清除字段错误
     */
    clearFieldError(field) {
        field.classList.remove('mdac-field-error');

        const errorMessage = field.parentNode.querySelector('.mdac-field-error-message');
        if (errorMessage) {
            errorMessage.remove();
        }
    }

    /**
     * 表单提交时验证
     */
    validateFormOnSubmit(event) {
        console.log('🔍 正在验证表单提交...');

        let isFormValid = true;
        const requiredFields = ['name', 'passNo', 'dob', 'nationality', 'sex', 'email'];

        requiredFields.forEach(fieldKey => {
            const field = this.formFields[fieldKey];
            if (field && !this.validateField(fieldKey, field)) {
                isFormValid = false;
            }
        });

        // 验证邮箱确认
        if (this.formFields.email && this.formFields.confirmEmail) {
            if (this.formFields.email.value !== this.formFields.confirmEmail.value) {
                this.showFieldError(this.formFields.confirmEmail, '邮箱确认不匹配');
                isFormValid = false;
            }
        }

        if (!isFormValid) {
            event.preventDefault();
            console.warn('⚠️ 表单验证失败，阻止提交');

            // 滚动到第一个错误字段
            const firstError = this.form.querySelector('.mdac-field-error');
            if (firstError) {
                firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                firstError.focus();
            }
        } else {
            console.log('✅ 表单验证通过');
        }

        return isFormValid;
    }

    /**
     * 显示字段检测状态
     */
    showFieldDetectionStatus(validation, stats) {
        // 创建状态显示容器
        const statusContainer = document.createElement('div');
        statusContainer.id = 'mdac-field-detection-status';
        statusContainer.className = 'mdac-detection-status';

        const confidence = parseFloat(validation.confidence);
        const statusClass = confidence >= 80 ? 'success' : confidence >= 60 ? 'warning' : 'error';

        statusContainer.innerHTML = `
            <div class="detection-header">
                <span class="detection-icon">🔍</span>
                <span class="detection-title">智能字段检测</span>
                <button class="detection-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
            <div class="detection-content">
                <div class="detection-stats">
                    <div class="stat-item ${statusClass}">
                        <span class="stat-label">检测置信度</span>
                        <span class="stat-value">${confidence}%</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">检测字段</span>
                        <span class="stat-value">${Object.keys(this.detectedFields).length}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">成功率</span>
                        <span class="stat-value">${stats.successRate}%</span>
                    </div>
                </div>
                ${validation.missingCriticalFields.length > 0 ? `
                    <div class="detection-warnings">
                        <div class="warning-title">⚠️ 缺失关键字段:</div>
                        <div class="warning-list">${validation.missingCriticalFields.join(', ')}</div>
                    </div>
                ` : ''}
                <div class="detection-actions">
                    <button class="detection-btn primary" onclick="window.mdacContentScript.showDetectedFields()">
                        查看检测结果
                    </button>
                    <button class="detection-btn secondary" onclick="window.mdacContentScript.redetectFields()">
                        重新检测
                    </button>
                </div>
            </div>
        `;

        // 添加样式
        this.addDetectionStatusStyles();

        // 插入到页面
        document.body.appendChild(statusContainer);

        // 5秒后自动隐藏（如果没有警告）
        if (validation.missingCriticalFields.length === 0) {
            setTimeout(() => {
                if (statusContainer.parentElement) {
                    statusContainer.remove();
                }
            }, 5000);
        }

        // 暴露方法到全局
        window.mdacContentScript = this;
    }

    /**
     * 设置消息监听器
     */
    setupMessageListener() {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true;
        });
    }

    /**
     * 处理来自扩展的消息
     */
    async handleMessage(message, sender, sendResponse) {
        try {
            switch (message.action) {
                case 'quickFill':
                    await this.quickFill();
                    sendResponse({ success: true });
                    break;

                case 'getFormData':
                    const formData = this.getFormData();
                    sendResponse({ success: true, data: formData });
                    break;

                case 'fillForm':
                case 'fillForm':
                case 'fillFormData':
                    await this.fillFormData(message.data, message.sessionId, message.fieldList);
                    sendResponse({ success: true });
                    break;

                case 'validateForm':
                    const validation = await this.validateForm();
                    sendResponse({ success: true, data: validation });
                    break;

                case 'analyzeAPI':
                    await this.analyzeAPI();
                    sendResponse({ success: true });
                    break;

                case 'getPageInfo':
                    const pageInfo = this.getPageInfo();
                    sendResponse({ success: true, data: pageInfo });
                    break;

                default:
                    sendResponse({ success: false, error: '未知的操作类型' });
            }
        } catch (error) {
            console.error('处理消息失败:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    /**
     * AI智能填充表单
     */
    async quickFill() {
        try {
            // 获取用户的默认数据
            const userData = await this.getUserData();

            if (!userData) {
                this.showNotification('请先在扩展中设置默认数据', 'warning');
                return;
            }

            // 使用AI智能填充
            await this.smartFill(userData);
            this.showNotification('AI智能填充完成', 'success');

        } catch (error) {
            console.error('AI智能填充失败:', error);
            this.showNotification('填充失败: ' + error.message, 'error');
        }
    }

    /**
     * AI智能填充
     */
    async smartFill(userData) {
        // 首先进行基础填充
        await this.basicFill(userData);

        // 然后进行AI验证和优化
        await this.aiValidateAndOptimize();
    }



    /**
     * 基础填充
     */
    async basicFill(userData) {
        const fieldMapping = {
            name: userData.name,
            passNo: userData.passportNo,
            dob: userData.dateOfBirth,
            nationality: userData.nationality,
            sex: userData.sex,
            passExpiry: userData.passportExpiry,
            email: userData.email,
            confirmEmail: userData.email,
            countryCode: userData.countryCode,
            mobileNo: userData.mobileNo,
            arrivalDate: userData.arrivalDate,
            departureDate: userData.departureDate,
            vesselNm: userData.flightNo,
            trvlMode: userData.modeOfTravel,
            embark: userData.lastPort,
            accommodationStay: userData.accommodation,
            accommodationAddress1: userData.address,
            accommodationAddress2: userData.address2,
            accommodationState: userData.state,
            accommodationPostcode: userData.postcode,
            accommodationCity: userData.city
        };

        for (const [fieldId, value] of Object.entries(fieldMapping)) {
            if (value && this.formFields[fieldId]) {
                await this.fillField(fieldId, value);
                await this.delay(100); // 避免填充过快
            }
        }
    }

    /**
     * 填充单个字段
     */
    async fillField(fieldId, value) {
        const field = this.formFields[fieldId];
        if (!field) return;

        try {
            if (field.tagName === 'SELECT') {
                // 下拉选择框
                field.value = value;
                field.dispatchEvent(new Event('change', { bubbles: true }));
            } else if (field.type === 'text' || field.type === 'email' || field.type === 'tel') {
                // 文本输入框
                field.focus();
                field.value = '';
                
                // 模拟逐字输入
                for (const char of value) {
                    field.value += char;
                    field.dispatchEvent(new Event('input', { bubbles: true }));
                    await this.delay(50);
                }
                
                field.dispatchEvent(new Event('change', { bubbles: true }));
                field.blur();
            }
        } catch (error) {
            console.error(`填充字段 ${fieldId} 失败:`, error);
        }
    }

    /**
     * AI验证和优化
     */
    async aiValidateAndOptimize() {
        try {
            const formData = this.getFormData();

            // 使用配置文件中的提示词模板
            const prompt = window.MDAC_AI_CONFIG.AI_PROMPTS.FORM_OPTIMIZATION.replace(
                '{formData}',
                JSON.stringify(formData, null, 2)
            );

            const context = window.MDAC_AI_CONFIG.AI_CONTEXTS.FORM_AUDITOR;

            const response = await chrome.runtime.sendMessage({
                action: 'callGeminiAI',
                prompt: prompt,
                context: context
            });

            if (response.success) {
                this.showAISuggestions(`✨ AI优化建议：<br><br>${response.data.replace(/\n/g, '<br>')}`);
                this.showNotification('AI分析完成，请查看建议', 'success');
            } else {
                this.showAISuggestions('AI分析暂时不可用，请检查网络连接和API配置。');
                this.showNotification('AI服务暂时不可用', 'error');
            }
        } catch (error) {
            console.error('AI验证失败:', error);
            this.showNotification('AI验证失败: ' + error.message, 'error');
        }
    }

    /**
     * 获取表单数据
     */
    getFormData() {
        const data = {};
        
        Object.keys(this.formFields).forEach(fieldId => {
            const field = this.formFields[fieldId];
            if (field && field.value) {
                data[fieldId] = field.value;
            }
        });

        return data;
    }

    /**
     * 获取用户数据
     */
    async getUserData() {
        try {
            const response = await chrome.runtime.sendMessage({
                action: 'getUserData'
            });
            
            if (response.success) {
                return response.data;
            }
            
            // 如果没有用户数据，返回默认测试数据
            return this.getDefaultTestData();
        } catch (error) {
            console.error('获取用户数据失败:', error);
            return this.getDefaultTestData();
        }
    }

    /**
     * 获取默认测试数据
     */
    getDefaultTestData() {
        return {
            name: 'ZHANG SAN',
            passportNo: 'A12345678',
            dateOfBirth: '01/01/1990',
            nationality: 'CHN',
            sex: '1',
            passportExpiry: '31/12/2030',
            email: '<EMAIL>',
            countryCode: '+60',
            mobileNo: '*********',
            arrivalDate: '10/01/2025',
            departureDate: '15/01/2025',
            flightNo: 'MH123',
            modeOfTravel: 'AIR',
            lastPort: 'SGP',
            accommodation: '01',
            address: 'KUALA LUMPUR CITY CENTER HOTEL',
            address2: 'JALAN BUKIT BINTANG',
            state: '14',
            postcode: '50000',
            city: '1400'
        };
    }

    /**
     * 注入AI助手界面
     */
    injectAIAssistant() {
        // 创建AI助手浮动面板
        const aiPanel = document.createElement('div');
        aiPanel.id = 'mdac-ai-assistant';
        aiPanel.innerHTML = `
            <div class="ai-header">
                <span class="ai-icon">🤖</span>
                <span class="ai-title">MDAC AI助手</span>
                <button class="ai-toggle" id="aiToggle">−</button>
            </div>
            <div class="ai-content" id="aiContent">
                <div class="ai-status">准备就绪</div>
                <div class="ai-suggestions" id="aiSuggestions"></div>
                <div class="ai-actions">
                    <button class="ai-btn" id="aiValidate">验证表单</button>
                    <button class="ai-btn" id="aiOptimize">优化建议</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(aiPanel);
        this.setupAIAssistantEvents();
    }

    /**
     * 设置AI助手事件
     */
    setupAIAssistantEvents() {
        const toggle = document.getElementById('aiToggle');
        const content = document.getElementById('aiContent');
        const validateBtn = document.getElementById('aiValidate');
        const optimizeBtn = document.getElementById('aiOptimize');

        toggle.addEventListener('click', () => {
            const isVisible = content.style.display !== 'none';
            content.style.display = isVisible ? 'none' : 'block';
            toggle.textContent = isVisible ? '+' : '−';
        });

        validateBtn.addEventListener('click', () => this.validateForm());
        optimizeBtn.addEventListener('click', () => this.aiValidateAndOptimize());
    }

    /**
     * 显示AI建议
     */
    showAISuggestions(suggestions) {
        const suggestionsDiv = document.getElementById('aiSuggestions');
        if (suggestionsDiv) {
            suggestionsDiv.innerHTML = suggestions.replace(/\n/g, '<br>');
        }
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `mdac-notification ${type}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    /**
     * 观察表单变化
     */
    observeFormChanges() {
        if (!this.form) return;

        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' || mutation.type === 'attributes') {
                    this.handleFormChange();
                }
            });
        });

        observer.observe(this.form, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['value', 'disabled']
        });
    }

    /**
     * 处理表单变化
     */
    handleFormChange() {
        // AI实时验证和建议
        this.debounce(() => {
            this.realtimeValidation();
        }, 500)();
    }

    /**
     * 实时验证
     */
    async realtimeValidation() {
        // 获取所有已填写的字段
        const filledFields = {};
        Object.keys(this.formFields).forEach(fieldId => {
            const field = this.formFields[fieldId];
            if (field && field.value) {
                filledFields[fieldId] = field.value;
            }
        });

        // 如果有足够的字段，进行AI验证
        if (Object.keys(filledFields).length >= 3) {
            await this.validateFieldsWithAI(filledFields);
        }
    }

    /**
     * 使用AI验证字段
     */
    async validateFieldsWithAI(fields) {
        try {
            // 检查是否启用实时验证
            if (!window.MDAC_AI_CONFIG.AI_FEATURES.REALTIME_VALIDATION.enabled) {
                return;
            }

            const prompt = `请验证以下MDAC表单字段的数据：

${Object.entries(fields).map(([key, value]) => `${key}: ${value}`).join('\n')}

请检查：
1. 数据格式是否正确
2. 字段间的逻辑关系
3. 是否符合马来西亚入境要求

请简洁回复验证结果和建议。`;

            const context = window.MDAC_AI_CONFIG.AI_CONTEXTS.FORM_VALIDATOR;

            const response = await chrome.runtime.sendMessage({
                action: 'callGeminiAI',
                prompt: prompt,
                context: context
            });

            if (response.success) {
                this.showAISuggestions(`🔍 实时验证：${response.data}`);
                this.highlightValidatedFields(fields, response.data);
            }
        } catch (error) {
            console.error('AI实时验证失败:', error);
        }
    }

    /**
     * 验证单个字段
     */
    async validateSingleField(fieldId, value) {
        try {
            const promptTemplate = window.MDAC_AI_CONFIG.AI_PROMPTS.FIELD_VALIDATION[fieldId];
            if (!promptTemplate) {
                return; // 没有对应的验证模板
            }

            const prompt = promptTemplate(value);
            const context = window.MDAC_AI_CONFIG.AI_CONTEXTS.FORM_VALIDATOR;

            const response = await chrome.runtime.sendMessage({
                action: 'callGeminiAI',
                prompt: prompt,
                context: context
            });

            if (response.success) {
                const field = this.formFields[fieldId];
                if (field) {
                    this.showFieldTooltip(field, response.data);
                    this.updateFieldValidationStatus(field, response.data);
                }
            }
        } catch (error) {
            console.error(`字段 ${fieldId} AI验证失败:`, error);
        }
    }

    /**
     * 高亮验证过的字段
     */
    highlightValidatedFields(fields, aiResult) {
        const isPositive = aiResult.toLowerCase().includes('正确') ||
                          aiResult.toLowerCase().includes('有效') ||
                          aiResult.toLowerCase().includes('符合');

        Object.keys(fields).forEach(fieldId => {
            const field = this.formFields[fieldId];
            if (field) {
                field.classList.remove('mdac-field-success', 'mdac-field-error');
                field.classList.add(isPositive ? 'mdac-field-success' : 'mdac-field-error');
            }
        });
    }

    /**
     * 防抖函数
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 延迟函数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 通知扩展
     */
    notifyExtension(event, data = {}) {
        chrome.runtime.sendMessage({
            action: 'contentScriptEvent',
            event: event,
            data: data
        });
    }



    /**
     * 获取页面信息
     */
    getPageInfo() {
        return {
            url: window.location.href,
            title: document.title,
            pageType: this.pageType,
            formExists: !!this.form,
            fieldsCount: Object.keys(this.formFields).length
        };
    }

    /**
     * AI智能内容解析
     */
    async parseContentWithAI(content) {
        try {
            // 检查是否启用内容解析功能
            if (!window.MDAC_AI_CONFIG.AI_FEATURES.CONTENT_PARSING.enabled) {
                this.showNotification('内容解析功能已禁用', 'warning');
                return null;
            }

            const prompt = window.MDAC_AI_CONFIG.AI_PROMPTS.CONTENT_PARSING.replace(
                '{content}',
                content
            );

            const context = window.MDAC_AI_CONFIG.AI_CONTEXTS.DATA_EXTRACTOR;

            const response = await chrome.runtime.sendMessage({
                action: 'callGeminiAI',
                prompt: prompt,
                context: context
            });

            if (response.success) {
                try {
                    // 尝试解析JSON
                    const cleanResult = response.data.replace(/```json|```/g, '').trim();
                    const extractedData = JSON.parse(cleanResult);

                    // 填充提取的数据
                    await this.fillExtractedData(extractedData);
                    this.showNotification('AI内容解析完成', 'success');

                    return extractedData;
                } catch (parseError) {
                    console.error('解析AI返回的JSON失败:', parseError);
                    this.showNotification('AI解析结果格式异常', 'error');
                }
            }
        } catch (error) {
            console.error('AI内容解析失败:', error);
            this.showNotification('AI内容解析失败', 'error');
        }
        return null;
    }

    /**
     * 填充提取的数据
     */
    async fillExtractedData(data) {
        for (const [fieldId, value] of Object.entries(data)) {
            if (value && this.formFields[fieldId]) {
                await this.fillField(fieldId, value);
                await this.delay(100);
            }
        }
    }

    /**
     * 填充表单数据（从popup调用）
     */
    async fillFormData(data, sessionId = null, fieldList = null) {
        let fillSessionId = sessionId;

        try {
            this.showNotification('开始填充表单数据...', 'info');

            // 准备字段列表
            const fields = fieldList || Object.entries(data).map(([key, value]) => ({
                key,
                label: this.getFieldLabel(key),
                value: value
            }));

            // 启动进度可视化
            if (this.progressVisualizer) {
                this.progressVisualizer.startProgress({
                    totalFields: fields.length,
                    fieldList: fields,
                    title: 'MDAC表单填充进度',
                    showInSidepanel: false,
                    position: 'bottom-right'
                });
            }

            // 启动填充监控会话
            if (this.fillMonitor && !fillSessionId) {
                fillSessionId = this.fillMonitor.startFillSession(
                    data,
                    this.formFields,
                    (progress) => this.updateFillProgress(progress)
                );
                this.currentFillSession = fillSessionId;
                console.log(`🚀 启动填充监控会话: ${fillSessionId}`);
            }

            // 显示填充进度
            this.showProgressIndicator();

            const fieldMapping = {
                name: 'name',
                passportNo: 'passNo',
                dateOfBirth: 'dob',
                nationality: 'nationality',
                sex: 'sex',
                passportExpiry: 'passExpiry',
                email: 'email',
                confirmEmail: 'confirmEmail',
                countryCode: 'countryCode',
                mobileNo: 'mobileNo',
                arrivalDate: 'arrivalDate',
                departureDate: 'departureDate',
                flightNo: 'vesselNm',
                modeOfTravel: 'trvlMode',
                lastPort: 'embark',
                accommodation: 'accommodationStay',
                address: 'accommodationAddress1',
                address2: 'accommodationAddress2',
                state: 'accommodationState',
                postcode: 'accommodationPostcode',
                city: 'accommodationCity'
            };

            let filledCount = 0;
            let failedCount = 0;
            const totalFields = Object.keys(data).filter(key => data[key]).length;

            for (const [dataKey, formFieldId] of Object.entries(fieldMapping)) {
                const value = data[dataKey];
                if (value && this.formFields[formFieldId]) {
                    try {
                        // 开始字段填充监控
                        if (this.fillMonitor && fillSessionId) {
                            this.fillMonitor.startFieldFill(dataKey);
                        }

                        // 更新进度可视化器 - 开始处理
                        if (this.progressVisualizer) {
                            this.progressVisualizer.updateFieldStatus(dataKey, 'processing');
                        }

                        await this.fillField(formFieldId, value);
                        filledCount++;

                        // 标记字段填充成功
                        if (this.fillMonitor && fillSessionId) {
                            this.fillMonitor.markFieldSuccess(dataKey, value);
                        }

                        // 更新进度可视化器 - 成功
                        if (this.progressVisualizer) {
                            this.progressVisualizer.updateFieldStatus(dataKey, 'success', { value });
                        }

                        console.log(`✅ 字段填充成功: ${dataKey} = ${value}`);

                    } catch (fieldError) {
                        failedCount++;

                        // 标记字段填充失败
                        if (this.fillMonitor && fillSessionId) {
                            this.fillMonitor.markFieldFailure(dataKey, fieldError.message);
                        }

                        // 更新进度可视化器 - 失败
                        if (this.progressVisualizer) {
                            this.progressVisualizer.updateFieldStatus(dataKey, 'failed', {
                                error: fieldError.message
                            });
                        }

                        console.error(`❌ 字段填充失败: ${dataKey}`, fieldError);
                    }

                    // 更新进度
                    this.updateProgress((filledCount / totalFields) * 100);
                    await this.delay(150); // 稍慢的填充速度，更自然
                } else {
                    // 字段不存在或值为空，标记为跳过
                    if (this.progressVisualizer) {
                        this.progressVisualizer.updateFieldStatus(dataKey, 'skipped', {
                            reason: value ? '字段不存在' : '值为空'
                        });
                    }
                }
            }

            // 隐藏进度指示器
            this.hideProgressIndicator();

            // 进行AI验证
            await this.aiValidateAndOptimize();

            // 结束填充会话
            if (this.fillMonitor && fillSessionId) {
                const finalStatus = failedCount === 0 ? 'success' :
                                  filledCount > 0 ? 'partial' : 'failed';
                this.fillMonitor.endFillSession(finalStatus);
                this.currentFillSession = null;
            }

            const successMessage = failedCount > 0
                ? `AI智能填充完成！成功填写${filledCount}个字段，${failedCount}个字段失败`
                : `AI智能填充完成！已填写${filledCount}个字段`;

            this.showNotification(successMessage, failedCount > 0 ? 'warning' : 'success');

            // 显示字段状态和保留数据（如果有增强表单填充器的结果）
            if (window.fieldStatusDisplay && this.lastFillResult) {
                // 显示填充状态
                if (this.lastFillResult.fillStats) {
                    window.fieldStatusDisplay.showStatus(
                        this.lastFillResult.fillStats,
                        this.lastFillResult.filledFields || [],
                        this.lastFillResult.failedFields || []
                    );
                }

                // 显示保留数据
                if (this.lastFillResult.preservedData) {
                    window.fieldStatusDisplay.showPreservedData(this.lastFillResult.preservedData);
                }
            }

        } catch (error) {
            console.error('填充表单数据失败:', error);
            this.hideProgressIndicator();

            // 结束填充会话（失败状态）
            if (this.fillMonitor && fillSessionId) {
                this.fillMonitor.endFillSession('failed');
                this.currentFillSession = null;
            }

            this.showNotification('填充失败: ' + error.message, 'error');

            // 使用错误恢复管理器处理错误
            if (this.errorRecoveryManager) {
                this.errorRecoveryManager.handleError(error, {
                    operation: 'fillFormData',
                    data: data,
                    sessionId: fillSessionId
                });
            }
        }
    }

    /**
     * 更新填充进度（FillMonitor回调）
     * @param {Object} progress 进度信息
     */
    updateFillProgress(progress) {
        console.log('📊 填充进度更新:', progress);

        // 更新现有的进度指示器
        const progressElement = document.querySelector('.mdac-progress-indicator .progress-text');
        if (progressElement) {
            progressElement.textContent = `填充进度: ${progress.percentage}% (${progress.successful}/${progress.total})`;
        }

        const progressBar = document.querySelector('.mdac-progress-indicator .progress-bar');
        if (progressBar) {
            progressBar.style.width = `${progress.percentage}%`;
        }

        // 显示详细状态
        const statusElement = document.querySelector('.mdac-progress-indicator .progress-status');
        if (statusElement) {
            statusElement.innerHTML = `
                <span class="status-item success">✅ ${progress.successful}</span>
                <span class="status-item failed">❌ ${progress.failed}</span>
                <span class="status-item skipped">⏭️ ${progress.skipped}</span>
                <span class="status-item progress">⏳ ${progress.inProgress}</span>
            `;
        }
    }

    /**
     * 显示填充进度指示器
     */
    showProgressIndicator() {
        // 移除现有的进度指示器
        const existingIndicator = document.querySelector('.mdac-progress-indicator');
        if (existingIndicator) {
            existingIndicator.remove();
        }

        // 创建新的进度指示器
        const indicator = document.createElement('div');
        indicator.className = 'mdac-progress-indicator';
        indicator.innerHTML = `
            <div class="progress-content">
                <div class="progress-icon">🤖</div>
                <div class="progress-text">AI正在智能填充表单...</div>
                <div class="mdac-progress-bar">
                    <div class="progress-bar" id="mdacProgressBar"></div>
                </div>
                <div class="progress-percentage" id="mdacProgressPercentage">0%</div>
                <div class="progress-status" id="mdacProgressStatus">
                    <span class="status-item success">✅ 0</span>
                    <span class="status-item failed">❌ 0</span>
                    <span class="status-item skipped">⏭️ 0</span>
                    <span class="status-item progress">⏳ 0</span>
                </div>
            </div>
        `;

        document.body.appendChild(indicator);
    }

    /**
     * 更新填充进度
     */
    updateProgress(percentage) {
        const progressBar = document.getElementById('mdacProgressBar');
        const progressPercentage = document.getElementById('mdacProgressPercentage');

        if (progressBar) {
            progressBar.style.width = percentage + '%';
        }

        if (progressPercentage) {
            progressPercentage.textContent = Math.round(percentage) + '%';
        }
    }

    /**
     * 隐藏填充进度指示器
     */
    hideProgressIndicator() {
        const indicator = document.querySelector('.mdac-progress-indicator');
        if (indicator) {
            setTimeout(() => {
                indicator.remove();
            }, 1000); // 延迟1秒移除，让用户看到完成状态
        }
    }

    /**
     * 智能地址翻译
     */
    async translateAddressWithAI(chineseAddress) {
        try {
            // 检查是否启用地址翻译功能
            if (!window.MDAC_AI_CONFIG.AI_FEATURES.ADDRESS_TRANSLATION.enabled) {
                this.showNotification('地址翻译功能已禁用', 'warning');
                return chineseAddress;
            }

            const prompt = window.MDAC_AI_CONFIG.AI_PROMPTS.ADDRESS_TRANSLATION.replace(
                '{address}',
                chineseAddress
            );

            const context = window.MDAC_AI_CONFIG.AI_CONTEXTS.ADDRESS_TRANSLATOR;

            const response = await chrome.runtime.sendMessage({
                action: 'callGeminiAI',
                prompt: prompt,
                context: context
            });

            if (response.success) {
                let result = response.data;

                // 如果启用了结果清理
                if (window.MDAC_AI_CONFIG.AI_FEATURES.ADDRESS_TRANSLATION.cleanResult) {
                    result = result.replace(/[^\w\s,.-]/g, '').trim();
                }

                this.showNotification('地址翻译完成', 'success');
                return result;
            }
        } catch (error) {
            console.error('地址翻译失败:', error);
            this.showNotification('地址翻译失败', 'error');
        }
        return chineseAddress; // 如果翻译失败，返回原地址
    }

    /**
     * 验证表单
     */
    async validateForm() {
        const formData = this.getFormData();
        const filledCount = Object.keys(formData).length;
        const totalFields = Object.keys(this.formFields).length;

        if (filledCount === 0) {
            this.showNotification('请先填写表单数据', 'warning');
            return;
        }

        // 使用AI进行全面验证
        await this.aiValidateAndOptimize();

        return {
            isValid: filledCount >= totalFields * 0.8, // 至少填写80%的字段
            filledCount: filledCount,
            totalFields: totalFields,
            completeness: (filledCount / totalFields * 100).toFixed(1)
        };
    }

    /**
     * 显示字段提示
     */
    showFieldTooltip(field, message) {
        // 移除现有的提示
        const existingTooltip = document.querySelector('.mdac-field-tooltip');
        if (existingTooltip) {
            existingTooltip.remove();
        }

        // 创建新的提示
        const tooltip = document.createElement('div');
        tooltip.className = 'mdac-field-tooltip';
        tooltip.textContent = message;

        // 确定提示类型
        const isPositive = message.toLowerCase().includes('正确') ||
                          message.toLowerCase().includes('有效') ||
                          message.toLowerCase().includes('符合');

        tooltip.classList.add(isPositive ? 'success' : 'warning');

        // 定位提示
        const rect = field.getBoundingClientRect();
        tooltip.style.position = 'absolute';
        tooltip.style.top = (rect.bottom + window.scrollY + 5) + 'px';
        tooltip.style.left = (rect.left + window.scrollX) + 'px';
        tooltip.style.zIndex = '10003';

        document.body.appendChild(tooltip);

        // 3秒后自动移除
        setTimeout(() => {
            tooltip.remove();
        }, 3000);
    }

    /**
     * 更新字段验证状态
     */
    updateFieldValidationStatus(field, aiResult) {
        const isPositive = aiResult.toLowerCase().includes('正确') ||
                          aiResult.toLowerCase().includes('有效') ||
                          aiResult.toLowerCase().includes('符合');

        field.classList.remove('mdac-field-success', 'mdac-field-error', 'mdac-field-highlight');
        field.classList.add(isPositive ? 'mdac-field-success' : 'mdac-field-error');
    }

    /**
     * 检测中文内容
     */
    containsChinese(text) {
        return /[\u4e00-\u9fff]/.test(text);
    }

    /**
     * 自动检测并翻译地址
     */
    async autoTranslateAddress(field) {
        if (!window.MDAC_AI_CONFIG.AI_FEATURES.ADDRESS_TRANSLATION.autoDetect) {
            return;
        }

        const value = field.value.trim();
        if (value && this.containsChinese(value)) {
            const translatedAddress = await this.translateAddressWithAI(value);
            if (translatedAddress !== value) {
                field.value = translatedAddress;
                field.dispatchEvent(new Event('change', { bubbles: true }));
                this.showNotification('地址已自动翻译为英文', 'success');
            }
        }
    }

    /**
     * 添加检测状态样式
     */
    addDetectionStatusStyles() {
        if (document.getElementById('mdac-detection-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'mdac-detection-styles';
        styles.textContent = `
            .mdac-detection-status {
                position: fixed;
                top: 20px;
                right: 20px;
                width: 320px;
                background: white;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                border: 1px solid #e1e5e9;
            }

            .detection-header {
                display: flex;
                align-items: center;
                padding: 12px 16px;
                background: #f8f9fa;
                border-bottom: 1px solid #e1e5e9;
                border-radius: 8px 8px 0 0;
            }

            .detection-icon {
                font-size: 16px;
                margin-right: 8px;
            }

            .detection-title {
                flex: 1;
                font-weight: 600;
                font-size: 14px;
                color: #333;
            }

            .detection-close {
                background: none;
                border: none;
                font-size: 18px;
                cursor: pointer;
                color: #666;
                padding: 0;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .detection-content {
                padding: 16px;
            }

            .detection-stats {
                display: grid;
                grid-template-columns: 1fr 1fr 1fr;
                gap: 12px;
                margin-bottom: 16px;
            }

            .stat-item {
                text-align: center;
                padding: 8px;
                border-radius: 4px;
                background: #f8f9fa;
            }

            .stat-item.success {
                background: #d4edda;
                color: #155724;
            }

            .stat-item.warning {
                background: #fff3cd;
                color: #856404;
            }

            .stat-item.error {
                background: #f8d7da;
                color: #721c24;
            }

            .stat-label {
                display: block;
                font-size: 11px;
                margin-bottom: 2px;
                opacity: 0.8;
            }

            .stat-value {
                display: block;
                font-size: 14px;
                font-weight: 600;
            }

            .detection-warnings {
                background: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: 4px;
                padding: 12px;
                margin-bottom: 16px;
            }

            .warning-title {
                font-weight: 600;
                font-size: 12px;
                color: #856404;
                margin-bottom: 4px;
            }

            .warning-list {
                font-size: 11px;
                color: #856404;
            }

            .detection-actions {
                display: flex;
                gap: 8px;
            }

            .detection-btn {
                flex: 1;
                padding: 8px 12px;
                border: none;
                border-radius: 4px;
                font-size: 12px;
                cursor: pointer;
                transition: all 0.2s;
            }

            .detection-btn.primary {
                background: #007bff;
                color: white;
            }

            .detection-btn.primary:hover {
                background: #0056b3;
            }

            .detection-btn.secondary {
                background: #6c757d;
                color: white;
            }

            .detection-btn.secondary:hover {
                background: #545b62;
            }
        `;

        document.head.appendChild(styles);
    }

    /**
     * 显示检测到的字段详情
     */
    showDetectedFields() {
        const modal = document.createElement('div');
        modal.className = 'mdac-detection-modal';
        modal.innerHTML = `
            <div class="modal-overlay" onclick="this.parentElement.remove()"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h3>检测到的表单字段</h3>
                    <button class="modal-close" onclick="this.parentElement.parentElement.remove()">×</button>
                </div>
                <div class="modal-body">
                    <div class="field-list">
                        ${Object.entries(this.detectedFields).map(([fieldType, element]) => `
                            <div class="field-item">
                                <div class="field-type">${fieldType}</div>
                                <div class="field-info">
                                    <span class="field-id">${element.id || element.name || 'N/A'}</span>
                                    <span class="field-tag">${element.tagName.toLowerCase()}</span>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;

        // 添加模态框样式
        this.addModalStyles();
        document.body.appendChild(modal);
    }

    /**
     * 重新检测字段
     */
    async redetectFields() {
        // 清除缓存
        if (this.fieldDetector) {
            this.fieldDetector.clearCache();
        }

        // 移除现有状态显示
        const existingStatus = document.getElementById('mdac-field-detection-status');
        if (existingStatus) {
            existingStatus.remove();
        }

        // 重新检测
        await this.detectFormFields();
    }

    /**
     * 添加模态框样式
     */
    addModalStyles() {
        if (document.getElementById('mdac-modal-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'mdac-modal-styles';
        styles.textContent = `
            .mdac-detection-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 10001;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            .modal-overlay {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
            }

            .modal-content {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                border-radius: 8px;
                width: 90%;
                max-width: 600px;
                max-height: 80%;
                overflow: hidden;
            }

            .modal-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 16px 20px;
                border-bottom: 1px solid #e1e5e9;
                background: #f8f9fa;
            }

            .modal-header h3 {
                margin: 0;
                font-size: 16px;
                color: #333;
            }

            .modal-close {
                background: none;
                border: none;
                font-size: 20px;
                cursor: pointer;
                color: #666;
                padding: 0;
                width: 24px;
                height: 24px;
            }

            .modal-body {
                padding: 20px;
                max-height: 400px;
                overflow-y: auto;
            }

            .field-list {
                display: grid;
                gap: 12px;
            }

            .field-item {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 12px;
                background: #f8f9fa;
                border-radius: 6px;
                border: 1px solid #e1e5e9;
            }

            .field-type {
                font-weight: 600;
                color: #333;
                font-size: 14px;
            }

            .field-info {
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .field-id {
                font-family: monospace;
                background: #e9ecef;
                padding: 2px 6px;
                border-radius: 3px;
                font-size: 12px;
                color: #495057;
            }

            .field-tag {
                background: #007bff;
                color: white;
                padding: 2px 6px;
                border-radius: 3px;
                font-size: 11px;
                text-transform: uppercase;
            }
        `;

        document.head.appendChild(styles);
    }

    /**
     * 统一错误处理方法
     */
    handleError(error, context = {}) {
        if (this.errorRecoveryManager) {
            this.errorRecoveryManager.handleError(error, {
                ...context,
                pageType: this.pageType,
                url: window.location.href
            });
        } else {
            // 降级处理
            console.error('错误处理失败:', error);
            this.showNotification('操作失败: ' + error.message, 'error');
        }
    }

    /**
     * 获取字段标签
     */
    getFieldLabel(fieldKey) {
        const labels = {
            name: '姓名',
            passportNo: '护照号码',
            dateOfBirth: '出生日期',
            nationality: '国籍',
            sex: '性别',
            passportExpiry: '护照到期日',
            email: '电子邮箱',
            confirmEmail: '确认邮箱',
            countryCode: '国家代码',
            mobileNo: '手机号码',
            arrivalDate: '到达日期',
            departureDate: '离开日期',
            flightNo: '航班号',
            modeOfTravel: '旅行方式',
            lastPort: '最后港口',
            accommodation: '住宿类型',
            address: '地址',
            address2: '地址2',
            state: '州/省',
            postcode: '邮政编码',
            city: '城市'
        };
        return labels[fieldKey] || fieldKey;
    }
}

// 初始化内容脚本
new MDACContentScript();
